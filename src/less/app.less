@import url(./tabs.less);
@import url(./list.less);
@import url(./table.less);
@import url(./antd.less);
@import url(./element.less);

p {
  margin: 0;
}

hr {
  border-bottom: none;
  border-left: none;
  border-right: none;
  border-top: 1px solid #e6e6e8;
  margin: 0;
}

img {
  object-fit: cover;
}

.flex {
  display: flex;
}

.content-area {
  width: 1440px;
  margin: 0 auto;
  position: relative;
}

@media screen and (min-width: 1921px) {
  .content-area {
    width: 1600px;
  }
}

@media screen and (max-width: 1480px) {
  .content-area {
    padding: 0 20px;
  }
}

.a {
  &:hover {
    color: var(--themeColor);
  }
}

.message {
  background: #f3f5f7;
  border-radius: 4px;
  padding: 12px;
  margin-left: 40px;
  margin-top: 8px;
  color: #222222;
  // line-height: 24px;
  margin-bottom: 12px;

  .content {
    font-weight: 600;
    color: #222222;
  }

  hr {
    margin: 8px 0;
  }

  .option {
    display: inline;
    line-height: 24px;
    font-size: 14px;

    span:first-child {
      color: rgba(34, 34, 34, 0.8);
      margin-right: 24px;
    }

    :deep(.ant-btn) {
      padding: 4px 12px;
    }
  }
}

.cursor {
  cursor: pointer;
}

.primary-color {
  // color: @primary-color;
  color: var(--themeColor);
}

.icon {
  width: 16px;
  margin-right: 8px;
}

.drawer-option {
  position: absolute;
  bottom: 0;
  width: 100%;
  border-top: 1px solid #e8e8e8;
  padding: 10px 16px;
  text-align: right;
  left: 0;
  background: #fff;
  border-radius: 0 0 4px 4px;
}

.ant-message {
  z-index: 2080;
}

.ant-message .anticon {
  vertical-align: text-bottom;
  top: 0;
}

// 菜单样式
.ant-menu:not(.ant-menu-horizontal) .ant-menu-item-selected {
  background-color: #fff;
}

.ant-drawer-body {
  padding-bottom: 53px;
}

.money {
  color: #fe6700;
}

.ant-btn-two-chinese-chars::first-letter {
  letter-spacing: 0;
}

.ant-btn {
  letter-spacing: 0 !important;

  i {
    margin-right: 8px;
    vertical-align: bottom;
  }

  img {
    margin-top: -0.125rem;
  }

  span {
    letter-spacing: 0;
    display: inline;
  }

  &>span {
    display: inline;
  }
}

.ant-btn:focus,
.ant-btn:hover {
  border-color: var(--themeColor) !important;
  color: var(--themeColor) !important;
}

.ant-btn.ant-btn-primary {
  background: var(--themeColor);
  color: #fff;
}

.ant-btn.ant-btn-primary:focus,
.ant-btn.ant-btn-primary:hover {
  border-color: var(--themeColor) !important;
  color: #fff !important;
}

.ant-btn-background-ghost {
  border-color: var(--themeColor) !important;
  color: var(--themeColor) !important;
  background: #FFFFFF;
}

.ant-btn.ant-btn-primary.ant-btn-background-ghost:focus,
.ant-btn.ant-btn-primary.ant-btn-background-ghost:hover {
  border-color: var(--themeColor) !important;
  color: var(--themeColor) !important;
  background: #FFFFFF;
}

// 分页鼠标滑过样式
.ant-pagination-prev {
  margin-right: 8px !important;
}

.ant-pagination-prev:focus .ant-pagination-item-link,
.ant-pagination-next:focus .ant-pagination-item-link,
.ant-pagination-prev:hover .ant-pagination-item-link,
.ant-pagination-next:hover .ant-pagination-item-link {
  color: var(--themeColor);
  border-color: var(--themeColor) !important;
}

.ant-pagination-prev:hover a,
.ant-pagination-next:hover a {
  border-color: var(--themeColor) !important;
}

.ant-pagination-item:focus,
.ant-pagination-item:hover {
  border-color: var(--themeColor) !important;
}



.ant-layout-content {
  position: relative;
}

.ant-layout-footer {
  padding: 23px 0;
  color: #999;

  a {
    color: #999;
  }
}

// .user-search {
//   .ant-input-search {
//     border: none;
//     border-bottom: 1px solid #f3f5f7;
//     padding-bottom: 16px;
//     .ant-input-search-icon {
//       font-size: 20px;
//     }
//   }
//   .ant-input-affix-wrapper-focused {
//     box-shadow: none;
//   }
// }
.ant-table-row {
  color: var(--text-80);

  .ant-badge-status-text {
    color: var(--text-80);
  }
}

.overflow {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
}

.mutiLineOverflow {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.login-bar {
  // display: flex;
  // justify-content: space-between;
  // align-items: center;
  width: 100%;
  position: absolute;
  bottom: 12px;
  box-sizing: border-box;
  left: 0;
  right: 12px;

  .login-policy {
    font-size: 12px;

    .policy-text {
      color: #a0a0a0;
    }
  }
}

//表单报错信息汇总
.mw-form {
  min-height: 360px;
  position: relative;

  .ant-form-item-has-error+.ant-form-item-has-error {
    .ant-form-item-explain.ant-form-item-explain-error {
      display: none;
    }
  }

  .ant-show-help-leave {
    display: none;
  }

  .ant-form-item-explain.ant-form-item-explain-error {
    position: absolute;
    top: 110px;
    font-size: 14px;
  }

  .ant-col {
    position: static;
  }

  .ant-form-item-with-help {
    margin-bottom: 16px;
  }

  .ant-form-item {
    margin-bottom: 16px;
  }

  .header {
    color: #595959;
    margin-bottom: 12px;
  }
}

.anticon {
  vertical-align: 0.1em !important;
}

.name-round {
  width: 80px;
  height: 80px;
  background: rgb(242, 219, 222);
  font-size: 30px;
  color: #ea0c28;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  line-height: 24px;
  font-size: 20px;
}

.ant-input-group.ant-input-group-compact>*:last-child.ant-select {
  //为了解决：联合搜索框，下拉框右边会有个黑线
  // border-right: none;
}

.bg-white {
  background-color: #fff;
}

//time-line样式
.ant-timeline-item {
  padding-top: 5px;
  padding-bottom: 10px;
  color: #595959;
}

.ant-timeline-item:first-child {
  color: #141414;
}

.ant-timeline-item-head {
  background: #d9d9d9;
  border: 2px solid #ffffff;
  width: 12px;
  height: 12px;
}

.ant-timeline-item:first-child .ant-timeline-item-head-blue {
  background: #595959;
  border: 2px solid #d9d9d9;
}

.ant-timeline-item-tail {
  left: 5px;
  border-style: dashed;
}

.allActive {
  .ant-timeline-item {
    color: #141414;
  }

  .ant-timeline-item-head {
    background: #595959;
    border: 2px solid #d9d9d9;
  }
}

.ant-calendar-selected-day .ant-calendar-date {
  color: #fff;
}

.scrollbar,
.ant-table-body {
  overflow-y: auto;
  overflow-x: auto;

  &::-webkit-scrollbar {
    /*滚动条整体样式*/
    width: 10px;
    /*高宽分别对应横竖滚动条的尺寸*/
    height: 10px;
  }

  &::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 10px;
    background-color: #d9d9d9;
  }

  &::-webkit-scrollbar-track {
    /*滚动条里面轨道*/
    background: #fff;
    border-radius: 10px;
  }
}

.text-white {
  color: #ffffff;
}

// 标题样式
.title {
  font-size: 16px;
  line-height: 24px;
  color: theme("colors.title");
}

.bg-linear-gray {
  background: linear-gradient(180deg, #FFFFFF 0%, #F5F7F7 100%);
}

:deep(.ant-table-thead > tr > th) {
  color: theme("colors.secondar-text");
}

.ant-input,
.ant-select {
  border-radius: 0;
}

::selection {
  background: var(--themeColor);
}

.ant-spin-dot-item {
  background-color: var(--themeColor) !important;
}

.btn-hover {
  line-height: 1;
  align-items: center;
  transition: all 0.3s;

  span {
    align-items: center;
  }

  .icon-box {
    position: relative;
    width: 20px;
    height: 20px;
    // margin-left: 2px;
    // opacity: 0.8;
  }

  .icon-default {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    opacity: 1;
    z-index: 1;
  }
}

.amap-logo {
  display: none !important;
}

.amap-copyright {
  opacity: 0 !important; //去掉高德的版本号
}
html{
  background-color: #3d3d3d;

}
.light{
  background-color:transparent !important
}
body {
  margin: 0; /* 移除默认边距 */
  min-height: 100%; /* 确保覆盖全屏 */
  background-color: var(--main-bg);
}
.dark{
  background-color:transparent !important;
  body {
    background-color: var(--main-bg);
  }
  .detail-info {
    // background: var(--bg-ff-dark);
      background-color: var(--second-bg) !important;
  }
}
 /* 适配 View Transitions API */
 ::view-transition-old(root),
 ::view-transition-new(root) {
     animation: none; /* 禁用默认动画 */
     mix-blend-mode: normal;
 }

 .chart-box {
  width: 100%;
  background: rgba(34, 34, 34, 0.04);
}
.dark {
  .chart-box {
      background: rgba(34, 34, 34, 0.1);
  }
  .dark\:bg-ff-dark{
    box-shadow: 0px 0px 10px 0px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
  }
}


// 首页tabs和详情页tabs样式问题
.tabs-content {
  border: 1px solid transparent;
  position: relative;
  .tabs-box {
      height: 173px;
  }
  &::after {
      display: block;
      content: '';
      width: 20px;
      height: 11px;
      // background: url(../assets/car/sj.png) no-repeat center center;
      background-size: 100% 100%;
      position: absolute;
      left: 50%;
      margin-left: -10px;
      top: 100%;
  }
  &:hover {
      border-color: var(--themeColor);
      &::after {
          // height: 11px;
          // background: url(../assets/car/sj1.png) no-repeat center center;
          // background-size: 100% 100%;
      }
  }
  &.active {
      border-color: var(--themeColor);
      &::after {
          height: 11px;
          background: url(../assets/car/sj1.png) no-repeat center center;
          background-size: 100% 100%;
      }
  }
}
.dark {
  .tabs-content {
      &.active {
          &::after {
              height: 11px;
              background: url(../assets/car/sj2.png) no-repeat center
                  center;
              background-size: 100% 100%;
          }
      }
      // &.active {
      //     filter: drop-shadow(0px 0px 1px rgb(111, 231, 255));
      //     .tabs-box {
      //         // box-shadow: 0px 0px 5px 2px rgba(62, 218, 205, 0.2);
      //         z-index: 2;
      //     }
      //     &::after {
      //         display: block;
      //         z-index: 1;
      //     }
      // }
      // &:hover {
      //     filter: drop-shadow(0px 0px 1px rgb(111, 231, 255));
      // }
  }
}
