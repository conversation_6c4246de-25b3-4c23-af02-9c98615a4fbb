<template>
    <div class="rank">
        <div class="text-title dark:text-title-dark">
            {{ $t('Charge/Discharge Ranking') }}
        </div>
        <div
            class="rank-header text-title dark:text-title-dark flex justify-between items-center text-center py-3"
        >
            <div class="rank-num h-5.5">
                {{ $t('Rank') }}
            </div>
            <div class="rank-name">
                {{ $t('Device No') }}
            </div>
            <div class="flex flex-1 items-center justify-end">
                <div>
                    {{
                        chargeType === 'charge' ? t('Charge') : t('Discharge')
                    }}({{ 'Ah' }})
                </div>
                <div
                    class="w-5 h-5 flex items-center justify-center cursor-pointer select-none text-center ml-3"
                    @click="toggleRank"
                >
                    <iconSvg name="toggle" :className="'toggle'" />
                </div>
            </div>
        </div>
        <div class="space-y-4">
            <template v-for="(item, index) in rankList" :key="index">
                <div
                    class="pr-1.5 flex items-center text-title dark:text-title-dark"
                    v-if="index < 5"
                >
                    <div class="h-5.5 rank-num">
                        {{ index + 1 }}
                    </div>
                    <div class="rank-name" :title="item.key">
                        {{ item.key }}
                    </div>
                    <div
                        class="flex-1 flex justify-end items-center w-0 rank-process"
                    >
                        <div class="leading-5.5 w-18 quantity text-center">
                            {{ item.quantity }}
                        </div>
                    </div>
                </div>
            </template>
            <template v-if="!rankList?.length">
                <empty-data
                    :description="$t('zanwushuju')"
                    class="mx-auto mt-10 text-secondar-text dark:text-60-darl"
                >
                    <slot name="empty"></slot>
                </empty-data>
            </template>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
const { t, locale } = useI18n()
const props = defineProps({
    rankList: {
        type: Array,
        default: () => [],
    },
})
const emits = defineEmits(['toggleRank'])
const toggleRank = () => {
    chargeType.value = chargeType.value === 'charge' ? 'discharge' : 'charge'
    emits('toggleRank', chargeType.value)
}
const chargeType = ref('charge')
</script>

<style lang="less" scoped>
.rank {
    width: 356px;
    // background: var(--bg-f5);
    border-radius: 6px;
    padding: 20px;
    border: 1px solid var(--border);
    background: var(--bg-white);
    .rank-num {
        width: 32px;
        text-align: center;
        margin-right: 4px;
    }

    .rank-name {
        width: 124px;
        text-align: left;
        padding-right: 4px;
    }

    .rank-process {
        flex: 1;

        .quantity {
        }
    }

    .toggle {
        width: 16px;
        height: 16px;
    }
}
</style>
