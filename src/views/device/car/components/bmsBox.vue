<template>
    <div class="pl-3">
        <div class="ul-box flex justify-between mb-5">
            <ul
                class="flex items-center flex-1 w-0 bg-title-t title-family pl-2.5"
            >
                <li class="mr-4" style="">
                    <span class="text-sm text-secondar-text dark:text-60-dark"
                        >{{ $t('Device Status') }}：</span
                    >
                    <span
                        class="origin mr-0.5"
                        v-if="data?.onlineStatus == 1"
                    ></span>
                    <span class="origin origin-gray mr-0.5" v-else></span>
                    <span class="text-sm text-title dark:text-title-dark">{{
                        data?.onlineStatus || data?.onlineStatus == 0
                            ? runningStatus[data?.onlineStatus]
                            : '-'
                    }}</span>
                </li>
                <li class="mr-4" style="">
                    <div class="flex items-center">
                        <span
                            class="text-sm text-secondar-text dark:text-60-dark"
                            >{{ $t('station_yunxingzhuangtai') }}：</span
                        >
                        <span class="text-sm">
                            <div
                                v-if="data.chargeStatus !== null"
                                class="flex items-center gap-x-1"
                                :style="{
                                    color: getState(data.chargeStatus).color,
                                }"
                            >
                                <i
                                    class="w-6 h-6 text-2xl leading-6"
                                    :class="[
                                        'iconfont',
                                        getState(data.chargeStatus).icon,
                                    ]"
                                ></i>
                                <span>{{
                                    getState(data.chargeStatus).label
                                }}</span>
                            </div>
                            <div v-else class="text-title dark:text-title-dark">
                                -
                            </div>
                        </span>
                    </div>
                </li>
                <li class="">
                    <span class="text-sm text-secondar-text dark:text-60-dark"
                        >{{ $t('Device No') }}：</span
                    ><span class="text-sm text-title dark:text-title-dark">{{
                        deviceData?.deviceSn
                    }}</span>
                </li>
            </ul>
            <div class="mr-1.5 flex items-center">
                <el-button plain round @click="lookBattery" linear>{{
                    $t('View Cell Analysis')
                }}</el-button>
            </div>
        </div>
        <div :style="objectStyle" class="flex flex-wrap place-content-start">
            <div class="descriptions-item mb-5">
                <div class="descriptions-item-label">
                    {{ $t('Vehicle type') }}：
                </div>
                <div class="descriptions-content">
                    {{ getVehicleType(deviceData?.vehicleType?.value) }}
                </div>
            </div>
            <div class="descriptions-item mb-5">
                <div class="descriptions-item-label">SOH：</div>
                <div class="descriptions-content">
                    {{ data?.soh || data.soh == 0 ? data.soh + ' %' : '-' }}
                </div>
            </div>
            <div class="descriptions-item mb-5">
                <div class="descriptions-item-label">
                    {{ $t('device_pingjunwendu') }}：
                </div>
                <div class="descriptions-content">
                    {{
                        data?.tempAvg || data?.tempAvg == 0
                            ? data?.tempAvg + ' °C'
                            : '-'
                    }}
                </div>
            </div>
            <div class="descriptions-item mb-5">
                <div class="descriptions-item-label">
                    {{ $t('Cell type') }}：
                </div>
                <div class="descriptions-content">
                    {{ deviceData.productModel }}
                </div>
            </div>
            <div class="descriptions-item mb-5">
                <div class="descriptions-item-label">SOC：</div>
                <div class="descriptions-content">
                    {{ data?.soc || data?.soc == 0 ? data?.soc + ' %' : '-' }}
                </div>
            </div>
            <div class="descriptions-item mb-5">
                <div class="descriptions-item-label">
                    {{ $t('device_zuigaowendu') }}：
                </div>
                <div class="descriptions-content">
                    {{
                        data?.tempMax || data?.tempMax == 0
                            ? data?.tempMax + ' °C'
                            : '-'
                    }}
                    {{ data?.tempMaxId ? ' | #' + data?.tempMaxId : '' }}
                </div>
            </div>
            <div class="descriptions-item mb-5">
                <div class="descriptions-item-label">
                    {{ $t('car_dianxingeshu') }}：
                </div>
                <div class="descriptions-content">
                    {{ data.cellCount }}
                </div>
            </div>
            <div class="descriptions-item mb-5">
                <div class="descriptions-item-label">
                    {{ $t('device_zongdianya') }}：
                </div>
                <div class="descriptions-content">
                    {{
                        data?.voltage || data.voltage == 0
                            ? data.voltage + ' V'
                            : '-'
                    }}
                </div>
            </div>

            <div class="descriptions-item mb-5">
                <div class="descriptions-item-label">
                    {{ $t('device_zuidiwendu') }}：
                </div>
                <div class="descriptions-content">
                    {{
                        data?.tempMin || data?.tempMin == 0
                            ? data?.tempMin + ' °C'
                            : '-'
                    }}
                    {{ data?.tempMinId ? ' | #' + data?.tempMinId : '' }}
                </div>
            </div>
            <div class="descriptions-item mb-5">
                <div class="descriptions-item-label">
                    {{ $t('car_edingrongliang') }}：
                </div>
                <div class="descriptions-content">
                    {{ data.ratedCapacity }} Ah
                </div>
            </div>
            <div class="descriptions-item mb-5">
                <div class="descriptions-item-label">
                    {{ $t('device_zongdianliu') }}：
                </div>
                <div class="descriptions-content">
                    {{
                        data?.electricity || data.electricity == 0
                            ? data.electricity.toFixed(1) + ' A'
                            : '-'
                    }}
                </div>
            </div>

            <div class="descriptions-item mb-5">
                <div class="descriptions-item-label">
                    {{ $t('device_pingjundianya') }}：
                </div>
                <div class="descriptions-content">
                    {{
                        data?.volAvg || data.volAvg === 0
                            ? data.volAvg + ' V'
                            : '-'
                    }}
                </div>
            </div>

            <div class="descriptions-item mb-5">
                <div class="descriptions-item-label">
                    {{ $t('Rated power') }}：
                </div>
                <div class="descriptions-content">
                    {{ deviceData.installedPower }} kW
                </div>
            </div>
            <div class="descriptions-item mb-5">
                <div class="descriptions-item-label">{{ $t('ins') }}：</div>
                <div class="descriptions-content">
                    {{ (data.insZ || 0) + ' kΩ | ' + (data.insF || 0) + ' kΩ' }}
                </div>
            </div>
            <div class="descriptions-item">
                <div class="descriptions-item-label">
                    {{ $t('device_zuigaodianya') }}：
                </div>
                <div class="descriptions-content">
                    {{
                        data?.volMax || data.volMax == 0
                            ? data.volMax + ' V'
                            : '-'
                    }}
                    {{ data?.volMaxId ? ' | #' + data?.volMaxId : '' }}
                </div>
            </div>

            <div class="descriptions-item mb-5">
                <div class="descriptions-item-label">
                    {{ $t('station_touyunriqi') }}：
                </div>
                <div class="descriptions-content">
                    {{
                        deviceData.bindTime
                            ? dayjs(deviceData.bindTime).format('YYYY/MM/DD')
                            : '-'
                    }}
                </div>
            </div>
            <div class="descriptions-item mb-5">
                <div class="descriptions-item-label">
                    {{ $t('car_leijixunhuancishu') }}：
                </div>
                <div class="descriptions-content">
                    {{ data.chargeCycles || 0 }}
                </div>
            </div>
            <div class="descriptions-item mb-5">
                <div class="descriptions-item-label">
                    {{ $t('device_zuididianya') }}：
                </div>
                <div class="descriptions-content">
                    {{ data?.volMin || data.volMin ? data.volMin + ' V' : '-' }}
                    {{ data?.volMinId ? ' | #' + data?.volMinId : '' }}
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { accountUnit, batteryStatus } from '../../const'
// import { getState } from '@/common/util.js'
import { computed, onMounted, ref } from 'vue'
import store from '@/store'
import dayjs from 'dayjs'
import { useI18n } from 'vue-i18n'
export default {
    name: 'bmsBox',
    props: {
        objectStyle: {
            type: Object,
            default: () => {
                return {
                    height: '100%',
                }
            },
        },
        data: {
            type: Object,
            default: () => {
                return {}
            },
        },
        deviceData: {
            type: Object,
            default: () => {
                return {}
            },
        },
    },
    setup(props, { emit }) {
        const { t } = useI18n()
        const lookBattery = () => {
            emit('lookBattery', true)
        }
        const vehicleTypeOptions = computed(() => {
            return store.state.dictionary.dictionaries.vehicleType || []
        })
        const getVehicleType = (val) => {
            if (vehicleTypeOptions.value.length == 0) return
            if (!val) return '-'
            return vehicleTypeOptions.value.find((item) => item.value == val)
                .label
        }

        onMounted(async () => {
            await store.dispatch('dictionary/getDictionary', 'vehicleType')
        })
        const runningStatus = ref({
            0: t('status_lixian'),
            1: t('status_zaixian'),
            2: t('NotActivated'),
        })
        const chargeState = ref([
            {
                label: t('status_chongdianzhong'),
                name: t('status_chongdian'),
                value: '1',
                icon: 'icon-ica-dianchi-fangdian',
                color: '#73ADFF',
                backGroundColor: '#73ADFF',
            },
            {
                label: t('status_fangdianzhong'),
                name: t('status_fangdian'),
                value: '2',
                icon: 'icon-ica-dianchi-chongdian',
                color: '#33BE4F',
                backGroundColor: '#33BE4F',
            },
            {
                label: t('status_daiji'),
                name: t('status_daiji'),
                value: '0',
                icon: 'icon-ica-dianchi-yunhang',
                color: '#222222',
                backGroundColor: '#d9d9d9',
            },
            {
                label: t('status_lixian'),
                name: t('status_lixian'),
                value: '3',
                icon: 'icon-ica-dianchi-lixian',
                color: '#666666',
                backGroundColor: '#666666',
            },
        ])
        const getState = (status) => {
            const item = chargeState.value.find((s) => s.value == status) || {}
            return item
        }

        return {
            accountUnit,
            batteryStatus,
            runningStatus,
            lookBattery,
            getState,
            getVehicleType,
            vehicleTypeOptions,
            dayjs,
        }
    },
}
</script>

<style scoped lang="less">
.descriptions-item {
    width: 33.33%;
    display: flex;
    border-right-style: solid;
    border-right-color: var(--border);
    border-right-width: 1px;
    font-size: 14px;
    font-family: AlibabaPuHuiTi_2_55_Regular;

    .descriptions-item-label {
        color: var(--text-60);
        padding-left: 32px;
        width: 60%;
        // width: 50%;
    }

    .descriptions-content {
        color: var(--text-100);
    }

    &:nth-child(3n) {
        border-right-color: transparent !important;
    }

    &:nth-child(3n-2) {
        .descriptions-item-label {
            padding-left: 9px;
        }
    }
}

.origin {
    display: inline-block;
    width: 6px;
    height: 6px;
    background-color: rgba(51, 190, 79, 1);
    vertical-align: middle;
    border-radius: 50%;
    &.origin-gray {
        background-color: rgba(34, 34, 34, 0.5);
    }
}

.ul-box {
    // height: 40px;
    padding: 10px 0;
    // line-height: 40px;
    line-height: 20px;
    background: var(--bg-f5);
}

.pt-6 {
    padding-top: 24px !important;
}

.text-sm {
    font-size: 14px !important;
    line-height: 20px !important;
}

.pl-8 {
    padding-left: 32px !important;
}

.ml-1 {
    margin-left: 4px !important;
}

.mb-5 {
    margin-bottom: 20px !important;
}
</style>
