<template>
    <el-drawer
        v-model="drawerVisible"
        :size="486"
        :lockScroll="true"
        :show-close="false"
    >
        <template #header>
            <div
                class="drawer-header flex items-center justify-between leading-5.5"
            >
                <div class="drawer-header">
                    <span>{{ $t('Device Management') }}</span>
                </div>
                <div class="flex gap-x-3">
                    <el-button plain round @click="closeDrawer">{{
                        $t('common_guanbi')
                    }}</el-button>
                    <el-button
                        plain
                        round
                        @click="onSave"
                        type="primary"
                        :loading="addLoading"
                        >{{ $t('Save') }}</el-button
                    >
                </div>
            </div>
        </template>
        <div>
            <div class="flex justify-between items-center mb-3">
                <div class="text-secondar-text dark:text-60-dark">
                    {{ $t('station_shebeixinxi') }}:
                </div>
                <el-button
                    plain
                    round
                    size="small"
                    type="primary"
                    @click="addNewDevice"
                    >{{ $t('add_device') }}</el-button
                >
            </div>
            <el-form
                :label-width="labelWidth"
                style="max-width: 600px"
                label-position="left"
            >
                <div
                    class="bg-background dark:bffffg-ff-dark px-3 py-4 mb-3 rounded rounded"
                    v-for="(item, index) in bindDeviceList"
                    :key="index"
                >
                    <el-form-item :label="$t('device_shebeimingcheng')">
                        <el-input
                            v-model="item.deviceName"
                            :placeholder="$t('placeholder_qingshuru')"
                        />
                    </el-form-item>
                    <el-form-item :label="$t('Device No')">
                        <el-input
                            v-model="item.deviceSn"
                            :disabled="!!item.bindTime"
                            :placeholder="$t('placeholder_qingshuru')"
                        />
                    </el-form-item>
                    <el-form-item :label="$t('Vehicle type')">
                        <el-select
                            v-model="item.vehicleType"
                            :placeholder="$t('placeholder_qingxuanze')"
                        >
                            <el-option
                                :label="ite.label"
                                :value="ite.value"
                                v-for="ite in vehicleTypes"
                                :key="ite.value"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item :label="$t('device_dianxinxinghao')">
                        <el-select
                            v-model="item.productModel"
                            :placeholder="$t('placeholder_qingxuanze')"
                        >
                            <el-option
                                :label="ite.label"
                                :value="ite.value"
                                v-for="ite in productModels"
                                :key="ite.value"
                            />
                        </el-select>
                    </el-form-item>
                    <div
                        class="flex justify-center items-center gap-x-3 text-xs"
                    >
                        <el-button
                            round
                            size="small"
                            linear
                            @click="deleteItem(index)"
                            >{{ $t('Delete') }}</el-button
                        >
                        <el-button
                            plain
                            round
                            size="small"
                            type="primary"
                            @click="copyItem(index)"
                            >{{ $t('Copy') }}</el-button
                        >
                    </div>
                </div>
            </el-form>
        </div>
    </el-drawer>
</template>

<script setup>
import { reactive, ref, watch, computed, onMounted, nextTick, toRaw } from 'vue'
import { useStore } from 'vuex'
import apiService from '@/apiService/device'
import carApi from '@/apiService/car'
import { roundNumFun, operationStatuss } from '@/views/device/const.js'
import dayjs from 'dayjs'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import store from '@/store'
import _cloneDeep from 'lodash/cloneDeep'
import { useI18n } from 'vue-i18n'
const { t, locale } = useI18n()
const labelWidth = computed(() => {
    let res =
        locale.value == 'zh' ? '70px' : locale.value == 'en' ? '130px' : '140px'
    return res
})
const router = useRouter(),
    route = useRoute()
const props = defineProps({
    visible: Boolean,
    deviceList: {
        type: Array,
        default: () => [],
    },
})
const emit = defineEmits(['onClose', 'update'])
watch(
    () => props.visible,
    async (val) => {
        if (val) {
            console.log('[ props.deviceList ] >', props.deviceList)
            store.dispatch('dictionary/getDictionary', 'vehicleType')
            store.dispatch('dictionary/getDictionary', 'vehicleBatteryModel')
            let data = props.deviceList.map((item) => {
                return {
                    ...item,
                    vehicleType: item.vehicleType?.value || '',
                }
            })
            bindDeviceList.value = _cloneDeep(data)
        }
    },
    { immediate: true }
)
const bindDeviceList = ref([])
watch(
    () => props.deviceList,
    async (val) => {},
    { immediate: true, deep: true }
)
const closeDrawer = () => {
    //
    // editDeviceVisible.value = false
    emit('update:visible', false)
}
const addLoading = ref(false)
const hasUniqueIds = (arr, type) => {
    const seenIds = new Set()
    for (const item of arr) {
        if (seenIds.has(item[type])) {
            return false
        }
        seenIds.add(item[type])
    }
    return true
}
const onSave = async () => {
    //
    let hasRepeatDeviceName = hasUniqueIds(bindDeviceList.value, 'deviceName')
    let hasRepeatDeviceSn = hasUniqueIds(bindDeviceList.value, 'deviceSn')
    if (bindDeviceList.value.some((item) => !item.deviceName)) {
        addLoading.value = false
        ElMessage.error(t('tianjiashebei_tips05'))
    } else if (bindDeviceList.value.some((item) => !item.deviceSn)) {
        addLoading.value = false
        ElMessage.error(t('tianjiashebei_tips02'))
    } else if (bindDeviceList.value.some((item) => !item.vehicleType)) {
        addLoading.value = false
        ElMessage.error(t('tianjiashebei_tips06'))
    } else if (bindDeviceList.value.some((item) => !item.productModel)) {
        addLoading.value = false
        ElMessage.error(t('tianjiashebei_tips07'))
    } else if (!hasRepeatDeviceName) {
        addLoading.value = false
        ElMessage.error(t('tianjiashebei_tips08'))
    } else if (!hasRepeatDeviceSn) {
        addLoading.value = false
        ElMessage.error(t('tianjiashebei_tips03'))
    } else {
        const params = {
            stationId: route.query.stationId,
            bindDeviceList: bindDeviceList.value.map((item) => {
                return {
                    deviceName: item.deviceName,
                    deviceSn: item.deviceSn,
                    vehicleType: item.vehicleType,
                    productModel: item.productModel,
                }
            }),
        }

        try {
            console.log('[ params ] >', params)
            let res = await carApi.updateStationDevicesVehicle(params)
            if (res.data.code === 0) {
                ElMessage.success(t('Successed'))
                const currentRoute = router.currentRoute.value
                if (currentRoute.path == '/carSystem/detail') {
                    emit('update')
                    closeDrawer()
                }
            }

            addLoading.value = false
        } catch (error) {
            addLoading.value = false
        }
    }
}
const drawerVisible = computed({
    get() {
        return props.visible
    },
    set(val) {
        emit('update:visible', val)
    },
})
const vehicleTypes = computed(
    () => store.state.dictionary.dictionaries.vehicleType || []
)
const productModels = computed(
    () => store.state.dictionary.dictionaries.vehicleBatteryModel || []
)
const addNewDevice = () => {
    bindDeviceList.value.push({
        deviceName: '',
        deviceSn: '',
        vehicleType: undefined,
        productModel: '',
    })
}
const deleteItem = (index) => {
    //
    if (bindDeviceList.value.length <= 1) {
        ElMessage.warning(t('tianjiashebei_tips09'))
    } else {
        bindDeviceList.value.splice(index, 1)
    }
}
const copyItem = (index) => {
    //
    bindDeviceList.value.splice(index + 1, 0, {
        deviceName: bindDeviceList.value[index].deviceName,
        deviceSn: bindDeviceList.value[index].deviceSn,
        vehicleType: bindDeviceList.value[index].vehicleType,
        productModel: bindDeviceList.value[index].productModel,
    })
}
</script>

<style scoped lang="less">
:deep(.el-form-item) {
    margin-bottom: 12px;
}

:deep(.el-form-item__label) {
    color: var(--text-60);
}
</style>
