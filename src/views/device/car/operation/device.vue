<template>
    <div class="custom-table flex flex-col">
        <!-- 列设置按钮 -->
        <div class="table-header flex justify-end gap-x-4 mt-4">
            <div class="flex items-center">
                <el-input
                    v-model="searchValueD"
                    :style="{ width: locale == 'en' ? '320px' : '260px' }"
                    :placeholder="$t('InputToSearch01')"
                >
                    <template #append>
                        <el-button plain :icon="Search" @click="onSearchD" />
                    </template>
                </el-input>
            </div>
            <el-button @click="showColumnSelector" plain round>
                {{ $t('Custom Columns') }}
                <iconSvg name="customList" :class="'w-4 h-4 ml-1'" />
            </el-button>
            <el-button @click="openFilter" plain round>
                <span>{{ $t('Device Filter') }}</span>
                <iconSvg name="filter" :class="'w-4 h-4 ml-1'" />
            </el-button>
            <el-button
                @click="openBatchBindDrawer"
                plain
                round
                v-if="isOperator"
            >
                {{ $t('Bind Devices') }}
                <iconSvg name="bind" :class="'w-4 h-4 ml-1'" />
            </el-button>
            <el-popconfirm
                class="box-item"
                :title="`${$t(
                    'Do you want to batch download the QR codes of the devices on the current page?'
                )}`"
                placement="top-start"
                @confirm="BatchDownloadQRCode"
                :confirm-button-text="$t('common_shi')"
                :cancel-button-text="$t('common_fou')"
                width="240px"
            >
                <template #reference>
                    <el-button
                        plain
                        round
                        :disabled="!tableData.length"
                        :loading="downloadQRLoading"
                    >
                        {{ $t('Download QR Code') }}
                        <iconSvg name="qrcode" :class="'w-4 h-4 ml-1'" />
                    </el-button>
                </template>
            </el-popconfirm>

            <el-dropdown
                split-button
                plain
                round
                @click="addDevice"
                placement="bottom-end"
            >
                <template #default>
                    <span class="mr-1">{{ $t('add_device') }}</span
                    ><iconSvg name="addDevice" :class="'w-4 h-4'" />
                </template>
                <template #dropdown>
                    <el-dropdown-menu>
                        <el-dropdown-item @click="addDeviceImport">{{
                            $t('batch_add')
                        }}</el-dropdown-item>
                    </el-dropdown-menu>
                </template>
            </el-dropdown>
        </div>
        <div class="flex-1 h-0 overflow-y-auto mt-5">
            <!-- 表格主体 -->
            <div class="w-full h-full">
                <el-auto-resizer>
                    <template #default="{ height, width }">
                        <el-table-v2
                            :columns="visibleColumns"
                            :data="tableData"
                            :width="width"
                            :height="height"
                            v-model:sort-state="sortState"
                            @column-sort="handleSortChange"
                            fixed
                            :row-height="70"
                            :row-event-handlers="{ onClick: rowClick }"
                        >
                            <template #empty>
                                <empty-data
                                    :description="$t('zanwushuju')"
                                    style="margin-top: 80px"
                                >
                                    <slot name="empty"></slot>
                                </empty-data>
                            </template>
                        </el-table-v2>
                    </template>
                </el-auto-resizer>
            </div>
        </div>
        <div class="flex justify-center mt-4">
            <el-pagination
                background
                :page-sizes="[10, 20, 30, 40]"
                layout="prev, pager, next, sizes"
                :total="devicePageTotal"
                v-model:current-page="devicePageInfo.current"
                :page-size="devicePageInfo.size"
                @change="devicePageChange"
                @size-change="handleDeviceSizeChange"
            />
        </div>

        <!-- 列选择器弹窗 -->
        <!-- 列选择器弹窗 -->
        <el-drawer
            v-model="dialogVisible"
            :size="500"
            :lockScroll="true"
            :show-close="false"
        >
            <template #header>
                <div
                    class="drawer-header flex items-center justify-between leading-5.5"
                >
                    <div class="drawer-header text-title dark:text-title-dark">
                        <span>{{ $t('column_settings') }}</span>
                    </div>
                    <div class="flex gap-x-3">
                        <el-button plain round @click="closeDrawer">{{
                            $t('common_guanbi')
                        }}</el-button>
                        <el-button plain round @click="onSave" type="primary">{{
                            $t('Confirm')
                        }}</el-button>
                    </div>
                </div>
            </template>
            <div class="flex items-start h-full">
                <div class="flex-1">
                    <div class="flex justify-start items-center">
                        <el-checkbox
                            v-model="checkAll"
                            :indeterminate="isIndeterminate"
                            @change="handleCheckAllChange"
                            style="width: auto"
                        >
                            {{ $t('Select all')
                            }}{{
                                checkedColumns.length
                                    ? '(' + (checkedColumns.length - 1) + ')'
                                    : ''
                            }}
                        </el-checkbox>
                        <!-- <div
                            class="ml-4 text-title dark:text-title-dark cursor-pointer select-none"
                            @click="onInvert"
                        >
                            反选
                        </div> -->
                    </div>
                    <el-checkbox-group
                        v-model="checkedColumns"
                        @change="onChangeCheckedColumns"
                        class="column-selector"
                    >
                        <el-checkbox
                            v-for="(item, index) in columnList"
                            :value="item.key"
                            :key="item.key"
                            :disabled="index < 2 || item.key === 'Operation'"
                            class="pl-4 relative"
                            v-show="item.key !== 'Operation'"
                        >
                            <div
                                class="absolute w-2.5 h-2.5 border-l border-b left-0 rounded-bl-sm opacity-60"
                            ></div>
                            {{ item.title }}
                        </el-checkbox>
                    </el-checkbox-group>
                </div>
                <el-divider direction="vertical" style="height: 100%" />
                <div class="flex-1 column-draggable">
                    <draggable
                        v-model="draggabledColumns"
                        item-key="key"
                        :disabled="false"
                        handle=".drag-handle"
                        @end="handleDragEnd"
                        :move="checkMove"
                    >
                        <template #item="{ element, index }">
                            <div
                                class="column-item"
                                v-show="element.key !== 'Operation'"
                            >
                                <el-icon
                                    class="drag-handle-disabled"
                                    v-if="index < 2"
                                >
                                    <iconSvg name="lock" class="w-5 h-5" />
                                </el-icon>
                                <el-icon class="drag-handle" v-else>
                                    <iconSvg name="drag" class="w-5 h-5" />
                                </el-icon>
                                <div
                                    class="text-title dark:text-title-dark flex-1"
                                    :class="
                                        index < 2
                                            ? ' opacity-40 select-none cursor-not-allowed'
                                            : ''
                                    "
                                >
                                    {{ element.title }}
                                </div>
                                <div
                                    class="w-6 h-6 flex items-center justify-center text-title dark:text-title-dark"
                                    :class="
                                        index < 2
                                            ? ' opacity-60 cursor-not-allowed'
                                            : 'cursor-pointer'
                                    "
                                    @click="onDeleteItem(element)"
                                >
                                    <el-icon size="14">
                                        <CloseBold />
                                    </el-icon>
                                </div>
                            </div>
                        </template>
                    </draggable>
                </div>
            </div>
        </el-drawer>
        <el-drawer
            v-model="dialogFormVisible"
            :size="486"
            :show-close="false"
            @close="cancelAdd"
            wrapClassName="drawerBox"
        >
            <template #header>
                <div
                    class="drawer-header flex items-center justify-between leading-5.5"
                >
                    <div
                        class="drawer-header text-primary-text dark:text-80-dark"
                    >
                        <span>{{ $t('add_device') }}</span>
                    </div>
                    <div class="flex gap-x-3 items-center">
                        <el-button plain round @click="cancelAdd">{{
                            $t('Cancle')
                        }}</el-button>
                        <el-button
                            plain
                            type="primary"
                            round
                            @click="confirmAdd()"
                            >{{ $t('Confirm') }}</el-button
                        >
                    </div>
                </div>
            </template>
            <div class="forms">
                <el-form
                    ref="formRef"
                    :model="formData"
                    :label-width="itemLabelWidth"
                    label-position="left"
                >
                    <el-form-item
                        :label="$t('Belonging project')"
                        prop="projectNo"
                    >
                        <el-select
                            v-model="formData.projectNo"
                            :placeholder="$t('placeholder_qingxuanze')"
                            class="w-full"
                        >
                            <el-option
                                v-for="item in projectData"
                                :key="item.projectNo"
                                :label="item.projectName"
                                :value="item.projectNo"
                            />
                        </el-select>
                    </el-form-item>
                    <div class="mb-2 text-primary-text dark:text-80-dark">
                        {{ $t('station_shebeixinxi') }}
                    </div>
                    <!-- 电池列表 -->
                    <div
                        v-for="(item, index) in formData.batteryList"
                        :key="index"
                        class="bg-background dark:bg-ffffff-dark px-3 py-4 mb-3 rounded"
                    >
                        <div class="flex items-center justify-between mb-1">
                            <div
                                class="font-medium text-title dark:text-title-dark"
                            >
                                {{ $t('Vehicles') }}{{ index + 1 }}
                            </div>
                        </div>

                        <el-form-item
                            :label="$t('Device No')"
                            :prop="`batteryList.${index}.sn`"
                        >
                            <el-input
                                v-model="item.sn"
                                :placeholder="$t('placeholder_qingshuru')"
                            />
                        </el-form-item>
                        <el-form-item
                            :label="$t('BatteryNo')"
                            :prop="`batteryList.${index}.batteryNo`"
                        >
                            <el-input
                                v-model="item.batteryNo"
                                :placeholder="$t('placeholder_qingshuru')"
                            />
                        </el-form-item>

                        <div
                            class="flex justify-center items-center gap-x-3 text-xs"
                        >
                            <el-button
                                @click="copyBatteryRow(item, index)"
                                plain
                                round
                                size="small"
                                type="primary"
                            >
                                {{ $t('Copy') }}
                            </el-button>
                            <el-button
                                v-if="formData.batteryList.length > 1"
                                @click="removeBatteryRow(index)"
                                round
                                size="small"
                                linear
                            >
                                {{ $t('Delete') }}
                            </el-button>
                        </div>
                    </div>

                    <!-- 添加更多设备按钮 -->
                    <div class="flex justify-center mt-4">
                        <el-button
                            @click="addBatteryRow"
                            type="primary"
                            plain
                            round
                        >
                            {{ $t('add_device') }}
                        </el-button>
                    </div>
                </el-form>
            </div>
        </el-drawer>
        <el-drawer
            v-model="importVisible"
            :size="486"
            :show-close="false"
            @close="cancelImport"
            wrapClassName="drawerBox"
        >
            <template #header>
                <div
                    class="drawer-header flex items-center justify-between leading-5.5"
                >
                    <div
                        class="drawer-header text-primary-text dark:text-80-dark"
                    >
                        <span>{{ $t('batch_add_device') }}</span>
                    </div>
                    <div class="flex gap-x-3 items-center">
                        <el-button plain round @click="cancelImport">{{
                            $t('Cancle')
                        }}</el-button>
                        <el-button
                            plain
                            type="primary"
                            round
                            @click="confirmImport()"
                            >{{ $t('Confirm') }}</el-button
                        >
                    </div>
                </div>
            </template>
            <div class="text-primary-text dark:text-80-dark">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="mb-2">{{ $t('step_one') }}:</div>
                        <div>{{ $t('step_one_desc') }}</div>
                    </div>
                    <div>
                        <el-button @click="downloadTemp" round>{{
                            $t('download_template')
                        }}</el-button>
                    </div>
                </div>
                <div class="mt-5">
                    <div class="mb-2">{{ $t('step_two') }}</div>
                    <div class="relative">
                        <el-upload
                            :on-change="handleFileChange"
                            :auto-upload="false"
                            accept=".xlsx, .xls"
                            ref="uploadRef"
                            :limit="1"
                            drag
                            :show-file-list="false"
                            :on-exceed="handleExceed"
                            :on-remove="() => (selectedFile = null)"
                            v-if="!selectedFile"
                        >
                            <template #trigger>
                                <el-icon class="el-icon--upload w-20" size="60"
                                    ><upload-filled
                                /></el-icon>
                                <div class="el-upload__text pt-2">
                                    {{ $t('drag_upload_tip') }}
                                </div>
                            </template>
                            <template #file v-if="false">
                                <div v-if="false"></div>
                            </template>
                        </el-upload>
                        <div
                            class="flex items-center border border-border text-center justify-center cursor-pointer relative file-box rounded"
                            v-else
                            style="height: 138px"
                        >
                            <div class="file-text">
                                {{ selectedFile.name }}
                            </div>
                            <div
                                @click="() => (selectedFile = null)"
                                class="absolute w-full h-full left-0 right-0 flex justify-center items-center align-middle delete-mask rounded"
                                style="line-height: 138px"
                            >
                                <el-icon size="30">
                                    <Delete />
                                </el-icon>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </el-drawer>
        <el-drawer
            v-model="filterDrawerVisible"
            direction="rtl"
            :size="486"
            :show-close="false"
            @close="onCloseFilterDrawer"
            wrapClassName="drawerBox"
        >
            <template #header>
                <div
                    class="drawer-header flex items-center justify-between leading-5.5"
                >
                    <div
                        class="drawer-header text-primary-text dark:text-80-dark"
                    >
                        <span>{{ $t('filter') }}</span>
                    </div>
                    <div class="flex gap-x-3">
                        <el-button plain round @click="onCloseFilterDrawer">{{
                            $t('common_guanbi')
                        }}</el-button>
                        <el-button
                            plain
                            round
                            @click="onFilterReset"
                            type="primary"
                            :loading="addLoading"
                            >{{ $t('reset') }}</el-button
                        >
                        <el-button
                            plain
                            round
                            @click="onFilterDrawerSave"
                            type="primary"
                            :loading="addLoading"
                            >{{ $t('Confirm') }}</el-button
                        >
                    </div>
                </div>
            </template>
            <div>
                <div class="top">
                    <el-tabs
                        v-model="activeFilterName"
                        @tab-change="handleFilterTabChange"
                    >
                        <template #default>
                            <el-tab-pane
                                :label="$t('filter_by_project')"
                                name="project"
                            >
                                <div class="project-filter">
                                    <div class="project-search">
                                        <el-input
                                            v-model="projectKeywords"
                                            style="max-width: 600px"
                                            :placeholder="
                                                $t('placeholder_qingshuru')
                                            "
                                            class="input-with-select"
                                        >
                                            <template #append>
                                                <el-button
                                                    @click="onSearchProject"
                                                >
                                                    <el-icon>
                                                        <Search />
                                                    </el-icon>
                                                </el-button>
                                            </template>
                                        </el-input>
                                    </div>
                                    <div class="flex-1 h-0 overflow-auto py-3">
                                        <div
                                            class="flex justify-start items-center"
                                        >
                                            <el-checkbox
                                                v-model="checkAllFilter"
                                                :indeterminate="
                                                    isIndeterminateFilter
                                                "
                                                @change="
                                                    handleCheckAllChangeFilter
                                                "
                                            >
                                                {{ $t('Select all') }}
                                            </el-checkbox>
                                            <el-button
                                                size="small"
                                                round
                                                @click="changeView"
                                                class="cursor-pointer flex items-center"
                                            >
                                                <!-- <div class="w-7">
                                                    {{
                                                        isName
                                                            ? $t('Name')
                                                            : $t('No')
                                                    }}
                                                </div> -->
                                                <iconSvg
                                                    name="toggle"
                                                    class="icon-default w-3.5 h-3.5"
                                                    style="
                                                        transform: rotate(
                                                            90deg
                                                        );
                                                    "
                                                />
                                            </el-button>
                                        </div>
                                        <el-checkbox-group
                                            v-model="checkedProject"
                                            @change="onChangeCheckedProject"
                                            class="column-selector"
                                        >
                                            <el-checkbox
                                                v-for="item in projectList"
                                                :value="item.id"
                                                :key="item.id"
                                                class="pl-4 relative"
                                            >
                                                <div
                                                    class="absolute w-2.5 h-2.5 border-l border-b left-0 rounded-bl-sm opacity-60"
                                                ></div>
                                                {{
                                                    isName
                                                        ? item.projectName
                                                        : item.projectNo
                                                }}
                                                /
                                                <span
                                                    >({{
                                                        item.batteryCount
                                                    }})</span
                                                >
                                            </el-checkbox>
                                        </el-checkbox-group>
                                    </div>
                                </div>
                            </el-tab-pane>
                            <el-tab-pane
                                :label="$t('filter_by_customer')"
                                name="customer"
                            >
                                <div class="project-filter">
                                    <!-- <div class="project-search">
                                        <el-input
                                            v-model="customKeywords"
                                            style="max-width: 600px"
                                            :placeholder="
                                                $t('placeholder_qingshuru')
                                            "
                                            class="input-with-select"
                                        >
                                            <template #append>
                                                <el-button
                                                    @click="onSearchCustom"
                                                >
                                                    <el-icon>
                                                        <Search />
                                                    </el-icon>
                                                </el-button>
                                            </template>
                                        </el-input>
                                    </div> -->
                                    <div
                                        class="flex-1 h-0 overflow-y-auto pb-3 element-plus-tree"
                                    >
                                        <el-tree
                                            ref="customerTreeRef"
                                            :data="treeData"
                                            show-checkbox
                                            node-key="value"
                                            default-expand-all
                                            :props="{
                                                label: 'label',
                                                children: 'children',
                                            }"
                                            @check="handleCustomerCheck"
                                            check-strictly
                                        />
                                        <div
                                            class="flex-1"
                                            v-if="treeData?.length <= 0"
                                        >
                                            <el-empty></el-empty>
                                        </div>
                                    </div>
                                </div>
                            </el-tab-pane>
                            <!-- <el-tab-pane :label="$t('filter_by_group')" name="group">
                                <div class="project-filter ">
                                    <div class="project-search">
                                        <el-input
                                            v-model="customKeywords"
                                            style="max-width: 600px"
                                            :placeholder="
                                                $t('placeholder_qingshuru')
                                            "
                                            class="input-with-select"
                                        >
                                            <template #append>
                                                <el-button
                                                    @click="onSearchCustom"
                                                >
                                                    <el-icon>
                                                        <Search />
                                                    </el-icon>
                                                </el-button>
                                            </template>
                                        </el-input>
                                    </div>
                                    <div class="flex-1">
                                        <el-empty></el-empty>
                                    </div>
                                </div>
                            </el-tab-pane> -->
                        </template>
                    </el-tabs>
                    <div class="filter-other">
                        <div>
                            <el-form
                                ref="formRef"
                                :model="formState"
                                :rules="rules"
                                :label-width="labelWidth"
                                label-position="left"
                            >
                                <el-form-item
                                    :label="$t('Device model')"
                                    prop="model"
                                >
                                    <el-select
                                        v-model="formState.model"
                                        :placeholder="
                                            $t('placeholder_qingxuanze')
                                        "
                                        multiple
                                    >
                                        <el-option
                                            v-for="item in deviceOptions"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value"
                                        />
                                    </el-select>
                                </el-form-item>
                                <el-form-item
                                    :label="$t('Region')"
                                    prop="cities"
                                >
                                    <el-tree-select
                                        v-model="formState.cities"
                                        :data="cityOptions"
                                        multiple
                                        :render-after-expand="true"
                                    />
                                </el-form-item>
                                <el-form-item
                                    :label="$t('Activation time')"
                                    prop="orgName"
                                >
                                    <el-date-picker
                                        v-model="formState.createTimeRange"
                                        type="daterange"
                                        range-separator="-"
                                        :start-placeholder="
                                            $t('common_kaishiriqi')
                                        "
                                        :end-placeholder="
                                            $t('common_jieshuriqi')
                                        "
                                        value-format="YYYY-MM-DD"
                                        :shortcuts="shortcuts"
                                    />
                                </el-form-item>
                            </el-form>
                        </div>
                    </div>
                </div>
            </div>
        </el-drawer>
        <!-- 设备绑定抽屉 -->
        <el-drawer
            v-model="bindDeviceVisible"
            :title="$t('Bind Single Device')"
            direction="rtl"
            :size="486"
            :show-close="false"
            @close="closeBindDrawer"
            wrapClassName="drawerBox"
        >
            <template #header>
                <div
                    class="drawer-header flex items-center justify-between leading-5.5"
                >
                    <div
                        class="drawer-header text-primary-text dark:text-80-dark"
                    >
                        <span>{{ $t('Bind Devices') }}</span>
                    </div>
                    <div class="flex gap-x-3 items-center">
                        <el-button plain round @click="closeBindDrawer">{{
                            $t('common_guanbi')
                        }}</el-button>
                        <el-button
                            v-if="bindingMode === 'edit'"
                            plain
                            type="primary"
                            round
                            @click="saveBinding"
                            >{{ $t('Save') }}</el-button
                        >
                        <el-button
                            v-if="bindingMode === 'view'"
                            plain
                            type="primary"
                            round
                            @click="bindingMode = 'edit'"
                            >{{ $t('common_bianji') }}</el-button
                        >
                    </div>
                </div>
            </template>
            <div v-if="currentDevice">
                <el-form
                    :model="bindingForm"
                    label-position="left"
                    :label-width="itemLabelWidth"
                >
                    <el-form-item :label="$t('Device No')">
                        <el-input :value="currentDevice.sn" disabled />
                    </el-form-item>
                    <el-form-item :label="$t('station_bangdingqiye')">
                        <el-tree-select
                            v-model="bindingForm.companyId"
                            :data="treeData"
                            :placeholder="$t('placeholder_qingxuanze')"
                            class="w-full"
                            :disabled="bindingMode === 'view'"
                            :props="{
                                label: 'label',
                                value: 'value',
                                children: 'children',
                            }"
                            :render-after-expand="false"
                            check-strictly
                            :default-expand-all="false"
                            node-key="value"
                        />
                    </el-form-item>
                </el-form>
                <div class="w-5 h-5 absolute z-1000 right-0 bottom-0">
                    <div
                        class="w-5 h-5 absolute z-10"
                        @dblclick="showDiv = true"
                    ></div>
                    <el-popconfirm
                        class="box-item"
                        :title="`Are you sure to unbind?`"
                        placement="top-start"
                        @confirm="unBindCustomer(currentDevice.id)"
                        @cancel="showDiv = false"
                        :confirm-button-text="$t('common_shi')"
                        :cancel-button-text="$t('common_fou')"
                        width="240px"
                        v-if="showDiv"
                    >
                        <template #reference>
                            <div class="w-5 h-5 absolute z-20"></div>
                        </template>
                    </el-popconfirm>
                </div>
            </div>
        </el-drawer>

        <!-- 查看二维码抽屉 -->
        <el-drawer
            v-model="viewQRVisible"
            :title="$t('Device QR Code')"
            direction="rtl"
            :size="486"
            :show-close="false"
            @close="viewQRVisible = false"
            wrapClassName="drawerBox"
        >
            <template #header>
                <div
                    class="drawer-header flex items-center justify-between leading-5.5"
                >
                    <div
                        class="drawer-header text-primary-text dark:text-80-dark"
                    >
                        <span>{{ $t('Device QR Code') }}</span>
                    </div>
                    <div class="flex gap-x-3 items-center">
                        <el-button plain round @click="viewQRVisible = false">{{
                            $t('Cancle')
                        }}</el-button>
                        <el-button
                            plain
                            type="primary"
                            round
                            @click="downloadQRCode"
                            >{{ $t('Download') }}</el-button
                        >
                    </div>
                </div>
            </template>
            <div
                v-if="currentDevice"
                class="flex flex-col items-center qrcode-text"
            >
                <!-- <p class="mt-5 text-base">{{ $t('Device QR Code') }}</p> -->
                <p class="mt-2 mb-4 text-60 dark:text-60-dark">
                    {{ $t('Device No') }}: {{ currentDevice.sn }}
                </p>
                <img
                    style="width: 250px; height: 250px"
                    :src="qrCodeUrl"
                    alt=""
                    srcset=""
                />
            </div>
        </el-drawer>
        <!-- 批量设备绑定抽屉 -->
        <el-drawer
            v-model="batchBindVisible"
            direction="rtl"
            :size="486"
            :show-close="false"
            @close="closeBatchBindDrawer"
            wrapClassName="drawerBox"
        >
            <template #header>
                <div
                    class="drawer-header flex items-center justify-between leading-5.5"
                >
                    <div
                        class="drawer-header text-primary-text dark:text-80-dark"
                    >
                        <span>{{ $t('Bind Devices') }}</span>
                    </div>
                    <div class="flex gap-x-3 items-center">
                        <el-button plain round @click="closeBatchBindDrawer">{{
                            $t('Cancle')
                        }}</el-button>
                        <el-button
                            plain
                            type="primary"
                            round
                            @click="confirmBatchBind"
                            >{{ $t('Confirm') }}</el-button
                        >
                    </div>
                </div>
            </template>
            <div class="h-full flex flex-col">
                <el-form
                    :model="batchBindForm"
                    label-position="left"
                    :label-width="itemLabelWidth"
                >
                    <el-form-item :label="$t('station_bangdingqiye')">
                        <el-tree-select
                            v-model="batchBindForm.companyId"
                            :data="treeData"
                            :placeholder="$t('placeholder_qingxuanze')"
                            class="w-full"
                            :props="{
                                label: 'label',
                                value: 'value',
                                children: 'children',
                            }"
                            :render-after-expand="false"
                            check-strictly
                            :default-expand-all="false"
                            node-key="value"
                        />
                    </el-form-item>
                </el-form>
                <div
                    class="flex-1 h-0 mt-4 border-t border-border dark:border-border-dark"
                >
                    <div
                        class="py-4 font-medium text-title dark:text-title-dark"
                    >
                        {{ $t('Choose Device') }}
                    </div>
                    <!-- 设备选择器将在这里实现 -->
                    <device-selector
                        ref="deviceSelectorRef"
                        v-model:selected-device-ids="batchBindForm.deviceIds"
                    />
                </div>
            </div>
        </el-drawer>
    </div>
</template>

<script setup>
import {
    ref,
    computed,
    watch,
    reactive,
    toRaw,
    onMounted,
    h,
    nextTick,
} from 'vue'
import {
    Setting,
    Operation,
    CloseBold,
    Filter,
    Search,
    UploadFilled,
    Delete,
    Link,
    View,
} from '@element-plus/icons-vue'
import {
    ElPopover,
    ElCheckbox,
    ElButton,
    ElIcon,
    ElRadio,
    ElDropdown,
    ElDropdownMenu,
    ElDropdownItem,
    ElTreeSelect,
} from 'element-plus'

import draggable from 'vuedraggable'
import powerApi from '@/apiService/power'
import apiService from '@/apiService/device'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useI18n } from 'vue-i18n'
const { t, locale } = useI18n()
import { useStore } from 'vuex'
import { useRoute, useRouter } from 'vue-router'
import { genFileId } from 'element-plus'
import { TableV2SortOrder } from 'element-plus'
import {
    formatterActiveStatus,
    getState,
    download,
    chargeStateP,
} from '@/common/util.js'
import dayjs from 'dayjs'
import iconSvg from '@/components/svgIcon'
import QRCode from '@/components/QRCode.vue'
import deviceSelector from '../components/deviceSelector.vue'

const store = useStore()
const router = useRouter()
// Props定义
// 表格数据
const tableData = ref([])
// 列配置相关
const columnList = ref([
    {
        title: t('Device No'),
        dataKey: 'sn',
        key: 'sn',
        width: 280,
        fixed: true,
        cellRenderer: ({ rowData }) => {
            return h('div', { class: 'flex flex-col' }, [
                h(
                    'div',
                    { class: 'text-primary-text dark:text-80-dark' },
                    rowData.sn
                ),
                h(
                    'div',
                    { class: ' secondar-text dark:text-60-dark' },
                    t('BatteryNo') + '：' + (rowData.batteryNo || '-')
                ),
            ])
        },
    },
    {
        title: 'HWID',
        dataKey: 'hwid',
        key: 'hwid',
        width: 180,
        cellRenderer: ({ rowData }) => {
            return h('div', { class: 'flex flex-col break-all' }, rowData.hwid)
        },
    },
    {
        title: 'IMEI',
        dataKey: 'imei',
        key: 'imei',
        width: 156,
    },
    {
        title: t('Vehicle type'),
        dataKey: 'vehicleType',
        key: 'vehicleType',
        width: 120,
        cellRenderer: ({ rowData }) => {
            return getVehicleType(rowData.vehicleType)
        },
        headerCellRenderer: () => {
            const shouldFilter = ref(false)
            const modelPopoverVisible = ref(false)
            const popoverRef = ref()

            const onFilter = () => {
                vehicleTypeFilter.value = selectedVehicleTypes.value
                getDeviceData()
                popoverRef.value?.hide()
            }

            const onReset = () => {
                selectedVehicleTypes.value = []
                vehicleTypeFilter.value = []
                getDeviceData()
                popoverRef.value?.hide()
            }

            return h('div', { class: 'flex items-center justify-center' }, [
                h('span', { class: 'mr-2 text-sm' }, t('Vehicle type')),
                h(
                    ElPopover,
                    {
                        trigger: 'click',
                        width: 200,
                        ref: popoverRef,
                        'onUpdate:visible': (val) =>
                            (modelPopoverVisible.value = val),
                    },
                    {
                        default: () =>
                            h('div', { class: 'filter-wrapper' }, [
                                h('div', { class: 'filter-options' }, [
                                    h(
                                        'div',
                                        { class: ' overflow-y-auto' },
                                        (vehicleTypes.value || []).map((item) =>
                                            h(
                                                ElCheckbox,
                                                {
                                                    modelValue:
                                                        selectedVehicleTypes.value.includes(
                                                            item.value
                                                        ),
                                                    'onUpdate:modelValue': (
                                                        val
                                                    ) => {
                                                        if (val) {
                                                            selectedVehicleTypes.value.push(
                                                                item.value
                                                            )
                                                        } else {
                                                            const index =
                                                                selectedVehicleTypes.value.indexOf(
                                                                    item.value
                                                                )
                                                            if (index > -1) {
                                                                selectedVehicleTypes.value.splice(
                                                                    index,
                                                                    1
                                                                )
                                                            }
                                                        }
                                                    },
                                                },
                                                () => item.label
                                            )
                                        )
                                    ),
                                ]),
                                h(
                                    'div',
                                    {
                                        class: 'flex justify-end gap-2 pt-2 border-t border-border dark:border-border-dark-008',
                                    },
                                    [
                                        h(
                                            ElButton,
                                            {
                                                onClick: onReset,
                                                round: true,
                                            },
                                            () => t('reset')
                                        ),
                                        h(
                                            ElButton,
                                            {
                                                type: 'primary',
                                                onClick: onFilter,
                                                round: true,
                                            },
                                            () => t('Confirm')
                                        ),
                                    ]
                                ),
                            ]),
                        reference: () =>
                            h(
                                'div',
                                {
                                    class:
                                        'cursor-pointer leading-4' +
                                        (vehicleTypeFilter.value?.length
                                            ? ' hasFilter'
                                            : ''),
                                    style: 'margin-top:3px',
                                },
                                [
                                    h(iconSvg, {
                                        name: 'filter',
                                        class: 'w-3.5 h-3.5 ',
                                    }),
                                ]
                            ),
                    }
                ),
            ])
        },
    },
    {
        title: t('Device model'),
        dataKey: 'model',
        key: 'model',
        width: 144,
        cellRenderer: ({ rowData }) => {
            // const model = deviceOptions.value.find(
            //     (item) => item.value === rowData.model
            // )
            // return model ? model.label : '-'
            return rowData.model ? rowData.model : '-'
        },
        headerCellRenderer: () => {
            const shouldFilter = ref(false)
            const modelPopoverVisible = ref(false)
            const popoverRef = ref()

            const onFilter = () => {
                modelFilter.value = selectedModels.value
                getDeviceData()
                popoverRef.value?.hide()
            }

            const onReset = () => {
                selectedModels.value = []
                modelFilter.value = []
                getDeviceData()
                popoverRef.value?.hide()
            }

            return h('div', { class: 'flex items-center justify-center' }, [
                h('span', { class: 'mr-2 text-sm' }, t('Device model')),
                h(
                    ElPopover,
                    {
                        trigger: 'click',
                        width: 200,
                        ref: popoverRef,
                        'onUpdate:visible': (val) =>
                            (modelPopoverVisible.value = val),
                    },
                    {
                        default: () =>
                            h('div', { class: 'filter-wrapper' }, [
                                h('div', { class: 'filter-options' }, [
                                    h(
                                        'div',
                                        { class: ' overflow-y-auto' },
                                        (deviceOptions.value || []).map(
                                            (item) =>
                                                h(
                                                    ElCheckbox,
                                                    {
                                                        modelValue:
                                                            selectedModels.value.includes(
                                                                item.value
                                                            ),
                                                        'onUpdate:modelValue': (
                                                            val
                                                        ) => {
                                                            if (val) {
                                                                selectedModels.value.push(
                                                                    item.value
                                                                )
                                                            } else {
                                                                const index =
                                                                    selectedModels.value.indexOf(
                                                                        item.value
                                                                    )
                                                                if (
                                                                    index > -1
                                                                ) {
                                                                    selectedModels.value.splice(
                                                                        index,
                                                                        1
                                                                    )
                                                                }
                                                            }
                                                        },
                                                    },
                                                    () => item.label
                                                )
                                        )
                                    ),
                                ]),
                                h(
                                    'div',
                                    {
                                        class: 'flex justify-end gap-2 pt-2 border-t border-border dark:border-border-dark-008',
                                    },
                                    [
                                        h(
                                            ElButton,
                                            {
                                                onClick: onReset,
                                                round: true,
                                            },
                                            () => t('reset')
                                        ),
                                        h(
                                            ElButton,
                                            {
                                                type: 'primary',
                                                onClick: onFilter,
                                                round: true,
                                            },
                                            () => t('Confirm')
                                        ),
                                    ]
                                ),
                            ]),
                        reference: () =>
                            h(
                                'div',
                                {
                                    class:
                                        'cursor-pointer leading-4' +
                                        (modelFilter.value?.length
                                            ? ' hasFilter'
                                            : ''),
                                    style: 'margin-top:3px',
                                },
                                [
                                    h(iconSvg, {
                                        name: 'filter',
                                        class: 'w-3.5 h-3.5 ',
                                    }),
                                ]
                            ),
                    }
                ),
            ])
        },
    },
    {
        title: t('activation_status'),
        dataKey: 'activeStatus',
        key: 'activeStatus',
        width: 144,
        cellRenderer: ({ rowData }) => {
            return t(formatterActiveStatus(rowData.activeStatus))
        },
        headerCellRenderer: () => {
            const shouldFilter = ref(false)
            const modelPopoverVisible = ref(false)
            const popoverRef = ref()

            const onFilter = () => {
                activeStatusFilter.value = selectedActiveStatus.value
                getDeviceData()
                popoverRef.value?.hide()
            }

            const onReset = () => {
                selectedActiveStatus.value = null
                activeStatusFilter.value = null
                getDeviceData()
                popoverRef.value?.hide()
            }

            return h('div', { class: 'flex items-center justify-center' }, [
                h('span', { class: 'mr-2 text-sm' }, t('activation_status')),
                h(
                    ElPopover,
                    {
                        trigger: 'click',
                        width: 200,
                        ref: popoverRef,
                        'onUpdate:visible': (val) =>
                            (modelPopoverVisible.value = val),
                    },
                    {
                        default: () =>
                            h('div', { class: 'filter-wrapper' }, [
                                h('div', { class: 'filter-options' }, [
                                    h('div', { class: ' overflow-y-auto' }, [
                                        h(
                                            ElRadio,
                                            {
                                                modelValue:
                                                    selectedActiveStatus.value,
                                                'onUpdate:modelValue': (
                                                    val
                                                ) => {
                                                    selectedActiveStatus.value =
                                                        val
                                                },
                                                label: 1,
                                            },
                                            () => t('Activated')
                                        ),
                                        h(
                                            ElRadio,
                                            {
                                                modelValue:
                                                    selectedActiveStatus.value,
                                                'onUpdate:modelValue': (
                                                    val
                                                ) => {
                                                    selectedActiveStatus.value =
                                                        val
                                                },
                                                label: 0,
                                            },
                                            () => t('NotActivated')
                                        ),
                                    ]),
                                ]),
                                h(
                                    'div',
                                    {
                                        class: 'flex justify-end gap-2 pt-2 border-t border-border dark:border-border-dark-008',
                                    },
                                    [
                                        h(
                                            ElButton,
                                            {
                                                onClick: onReset,
                                                round: true,
                                            },
                                            () => t('reset')
                                        ),
                                        h(
                                            ElButton,
                                            {
                                                type: 'primary',
                                                onClick: onFilter,
                                                round: true,
                                            },
                                            () => t('Confirm')
                                        ),
                                    ]
                                ),
                            ]),
                        reference: () =>
                            h(
                                'div',
                                {
                                    class:
                                        'cursor-pointer leading-4' +
                                        (activeStatusFilter.value?.length
                                            ? ' hasFilter'
                                            : ''),
                                    style: 'margin-top:3px',
                                },
                                [
                                    h(iconSvg, {
                                        name: 'filter',
                                        class: 'w-3.5 h-3.5 ',
                                    }),
                                ]
                            ),
                    }
                ),
            ])
        },
    },
    {
        title: t('system_status'),
        dataKey: 'status',
        key: 'status',
        width: 144,
        cellRenderer: ({ rowData }) => {
            return formatterStatus(rowData)
        },
        headerCellRenderer: () => {
            const shouldFilter = ref(false)
            const modelPopoverVisible = ref(false)
            const popoverRef = ref()

            const onFilter = () => {
                statusFilter.value = selectedStatus.value
                getDeviceData()
                popoverRef.value?.hide()
            }

            const onReset = () => {
                selectedStatus.value = []
                statusFilter.value = []
                getDeviceData()
                popoverRef.value?.hide()
            }

            return h('div', { class: 'flex items-center justify-center' }, [
                h('span', { class: 'mr-2 text-sm' }, t('system_status')),
                h(
                    ElPopover,
                    {
                        trigger: 'click',
                        width: 200,
                        ref: popoverRef,
                        'onUpdate:visible': (val) =>
                            (modelPopoverVisible.value = val),
                    },
                    {
                        default: () =>
                            h('div', { class: 'filter-wrapper' }, [
                                h('div', { class: 'filter-options' }, [
                                    h(
                                        'div',
                                        { class: ' overflow-y-auto' },
                                        chargeStateP.map((item) =>
                                            h(
                                                ElCheckbox,
                                                {
                                                    modelValue:
                                                        selectedStatus.value.includes(
                                                            item.value
                                                        ),
                                                    'onUpdate:modelValue': (
                                                        val
                                                    ) => {
                                                        if (val) {
                                                            selectedStatus.value.push(
                                                                item.value
                                                            )
                                                        } else {
                                                            const index =
                                                                selectedStatus.value.indexOf(
                                                                    item.value
                                                                )
                                                            if (index > -1) {
                                                                selectedStatus.value.splice(
                                                                    index,
                                                                    1
                                                                )
                                                            }
                                                        }
                                                    },
                                                },
                                                () => t(item.label)
                                            )
                                        )
                                    ),
                                ]),
                                h(
                                    'div',
                                    {
                                        class: 'flex justify-end gap-2 pt-2 border-t border-border dark:border-border-dark-008',
                                    },
                                    [
                                        h(
                                            ElButton,
                                            {
                                                onClick: onReset,
                                                round: true,
                                            },
                                            () => t('reset')
                                        ),
                                        h(
                                            ElButton,
                                            {
                                                type: 'primary',
                                                onClick: onFilter,
                                                round: true,
                                            },
                                            () => t('Confirm')
                                        ),
                                    ]
                                ),
                            ]),
                        reference: () =>
                            h(
                                'div',
                                {
                                    class:
                                        'cursor-pointer leading-4' +
                                        (statusFilter.value?.length
                                            ? ' hasFilter'
                                            : ''),
                                    style: 'margin-top:3px',
                                },
                                [
                                    h(iconSvg, {
                                        name: 'filter',
                                        class: 'w-3.5 h-3.5 ',
                                    }),
                                ]
                            ),
                    }
                ),
            ])
        },
    },
    {
        title: t('service_status'),
        dataKey: 'serviceStatus',
        key: 'serviceStatus',
        width: 144,
        cellRenderer: ({ rowData }) => {
            return formatterServiceStatus(rowData.serviceStatus)
        },
        headerCellRenderer: () => {
            const shouldFilter = ref(false)
            const modelPopoverVisible = ref(false)
            const popoverRef = ref()

            const onFilter = () => {
                serviceStatusFilter.value = selectedServiceStatus.value
                getDeviceData()
                popoverRef.value?.hide()
            }

            const onReset = () => {
                selectedServiceStatus.value = null
                serviceStatusFilter.value = null
                getDeviceData()
                popoverRef.value?.hide()
            }

            return h('div', { class: 'flex items-center justify-center' }, [
                h('span', { class: 'mr-2 text-sm' }, t('service_status')),
                h(
                    ElPopover,
                    {
                        trigger: 'click',
                        width: 200,
                        ref: popoverRef,
                        'onUpdate:visible': (val) =>
                            (modelPopoverVisible.value = val),
                    },
                    {
                        default: () =>
                            h('div', { class: 'filter-wrapper' }, [
                                h('div', { class: 'filter-options' }, [
                                    h('div', { class: ' overflow-y-auto' }, [
                                        h(
                                            ElRadio,
                                            {
                                                modelValue:
                                                    selectedServiceStatus.value,
                                                'onUpdate:modelValue': (
                                                    val
                                                ) => {
                                                    selectedServiceStatus.value =
                                                        val
                                                },
                                                label: 1,
                                            },
                                            () => t('正常')
                                        ),
                                        h(
                                            ElRadio,
                                            {
                                                modelValue:
                                                    selectedServiceStatus.value,
                                                'onUpdate:modelValue': (
                                                    val
                                                ) => {
                                                    selectedServiceStatus.value =
                                                        val
                                                },
                                                label: 2,
                                            },
                                            () => t('到期')
                                        ),
                                    ]),
                                ]),
                                h(
                                    'div',
                                    {
                                        class: 'flex justify-end gap-2 pt-2 border-t border-border dark:border-border-dark-008',
                                    },
                                    [
                                        h(
                                            ElButton,
                                            {
                                                onClick: onReset,
                                                round: true,
                                            },
                                            () => t('reset')
                                        ),
                                        h(
                                            ElButton,
                                            {
                                                type: 'primary',
                                                onClick: onFilter,
                                                round: true,
                                            },
                                            () => t('Confirm')
                                        ),
                                    ]
                                ),
                            ]),
                        reference: () =>
                            h(
                                'div',
                                {
                                    class:
                                        'cursor-pointer leading-4' +
                                        (serviceStatusFilter.value?.length
                                            ? ' hasFilter'
                                            : ''),
                                    style: 'margin-top:3px',
                                },
                                [
                                    h(iconSvg, {
                                        name: 'filter',
                                        class: 'w-3.5 h-3.5 ',
                                    }),
                                ]
                            ),
                    }
                ),
            ])
        },
    },
    {
        title: t('service_expire_time'),
        dataKey: 'serviceExpireDate',
        key: 'serviceExpireDate',
        width: 168,
        sortable: true,
        headerCellRenderer: ({ column, sortState }) => {
            return h('div', { class: 'flex items-center justify-center' }, [
                h('span', { class: 'mr-2 text-sm' }, column.title),
                getSortIcon('serviceExpireDate', sortState),
            ])
        },
    },
    {
        title: t('activation_date'),
        dataKey: 'activeTime',
        key: 'activeTime',
        width: 148,
        sortable: true,
        cellRenderer: ({ rowData }) => {
            return formatterActiveTime(rowData)
        },
        headerCellRenderer: ({ column, sortState }) => {
            return h('div', { class: 'flex items-center justify-center' }, [
                h('span', { class: 'mr-2 text-sm' }, column.title),
                getSortIcon('activeTime', sortState),
            ])
        },
    },
    {
        title: t('signal_strength'),
        dataKey: 'signal4g',
        key: 'signal4g',
        width: locale.value == 'zh' ? 100 : 140,
        sortable: true,
        headerCellRenderer: ({ column, sortState }) => {
            return h('div', { class: 'flex items-center justify-center' }, [
                h('span', { class: 'mr-2 text-sm' }, column.title),
                getSortIcon('signal4g', sortState),
            ])
        },
    },
    {
        title: t('last_data_time'),
        dataKey: 'lastHeartbeatTime',
        key: 'lastHeartbeatTime',
        width: 168,
        sortable: true,
        headerCellRenderer: ({ column, sortState }) => {
            return h('div', { class: 'flex items-center justify-center' }, [
                h('span', { class: 'mr-2 text-sm' }, column.title),
                getSortIcon('lastHeartbeatTime', sortState),
            ])
        },
    },
    {
        title: 'SOC',
        dataKey: 'soc',
        key: 'soc',
        width: 80,
        sortable: true,
        headerCellRenderer: ({ column, sortState }) => {
            return h('div', { class: 'flex items-center justify-center' }, [
                h('span', { class: 'mr-2 text-sm' }, column.title),
                getSortIcon('soc', sortState),
            ])
        },
    },
    {
        title: 'SOH',
        dataKey: 'soh',
        key: 'soh',
        width: 80,
        sortable: true,
        headerCellRenderer: ({ column, sortState }) => {
            return h('div', { class: 'flex items-center justify-center' }, [
                h('span', { class: 'mr-2 text-sm' }, column.title),
                getSortIcon('soh', sortState),
            ])
        },
    },
    {
        title: t('alarm_count'),
        dataKey: 'alarmNub',
        key: 'alarmNub',
        width: 80,
        sortable: true,
        headerCellRenderer: ({ column, sortState }) => {
            return h('div', { class: 'flex items-center justify-center' }, [
                h('span', { class: 'mr-2 text-sm' }, column.title),
                getSortIcon('alarmNub', sortState),
            ])
        },
    },
    {
        title: t('total_charge_time'),
        dataKey: 'chgTimeSum',
        key: 'chgTimeSum',
        width: 128,
        sortable: true,
        headerCellRenderer: ({ column, sortState }) => {
            return h('div', { class: 'flex items-center justify-center' }, [
                h('span', { class: 'mr-2 text-sm' }, column.title),
                getSortIcon('chgTimeSum', sortState),
            ])
        },
    },
    {
        title: t('total_charge_capacity'),
        dataKey: 'chgCapSum',
        key: 'chgCapSum',
        width: 148,
        sortable: true,
        headerCellRenderer: ({ column, sortState }) => {
            return h('div', { class: 'flex items-center justify-center' }, [
                h('span', { class: 'mr-2 text-sm' }, column.title),
                getSortIcon('chgCapSum', sortState),
            ])
        },
    },
    {
        title: t('total_discharge_time'),
        dataKey: 'dsgTimeSum',
        key: 'dsgTimeSum',
        width: 128,
        sortable: true,
        headerCellRenderer: ({ column, sortState }) => {
            return h('div', { class: 'flex items-center justify-center' }, [
                h('span', { class: 'mr-2 text-sm' }, column.title),
                getSortIcon('dsgTimeSum', sortState),
            ])
        },
    },
    {
        title: t('total_discharge_capacity'),
        dataKey: 'dsgCapSum',
        key: 'dsgCapSum',
        width: 148,
        sortable: true,
        headerCellRenderer: ({ column, sortState }) => {
            return h('div', { class: 'flex items-center justify-center' }, [
                h('span', { class: 'mr-2 text-sm' }, column.title),
                getSortIcon('dsgCapSum', sortState),
            ])
        },
    },
    {
        title: t('last_charge_time'),
        dataKey: 'lastChargeTime',
        key: 'lastChargeTime',
        width: 168,
        sortable: true,
        headerCellRenderer: ({ column, sortState }) => {
            return h('div', { class: 'flex items-center justify-center' }, [
                h('span', { class: 'mr-2 text-sm' }, column.title),
                getSortIcon('lastChargeTime', sortState),
            ])
        },
    },
    {
        title: t('last_discharge_time'),
        dataKey: 'lastDischargeTime',
        key: 'lastDischargeTime',
        width: 168,
        sortable: true,
        headerCellRenderer: ({ column, sortState }) => {
            return h('div', { class: 'flex items-center justify-center' }, [
                h('span', { class: 'mr-2 text-sm' }, column.title),
                getSortIcon('lastDischargeTime', sortState),
            ])
        },
    },
    {
        title: t('Cycle times'),
        dataKey: 'cycleCount',
        key: 'cycleCount',
        width: 128,
        sortable: true,
        headerCellRenderer: ({ column, sortState }) => {
            return h('div', { class: 'flex items-center justify-center' }, [
                h('span', { class: 'mr-2 text-sm' }, column.title),
                getSortIcon('cycleCount', sortState),
            ])
        },
    },
    {
        title: t('station_tianjiazhandian'),
        dataKey: 'customerName',
        key: 'customerName',
        width: 188,
    },

    {
        title: t('Operation'),
        dataKey: 'operation',
        key: 'Operation',
        width: 80,
        fixed: 'right',
        /**
         * @description: 操作列的单元格渲染器
         * @param {object} { rowData } - 行数据
         * @returns {VNode} - 渲染的 VNode
         */
        cellRenderer: ({ rowData }) => {
            return h(
                'div',
                { class: 'flex items-center justify-center mx-auto' },
                [
                    h(
                        ElDropdown,
                        {
                            trigger: 'click',
                            /**
                             * @description: 处理下拉菜单命令
                             * @param {string} command - 命令名称
                             */
                            onCommand: (command) =>
                                handleOperation(command, rowData),
                        },
                        {
                            default: () => [
                                h(
                                    'div',
                                    {
                                        class: 'cursor-pointer leading-4 outline-none border-none mx-auto w-10 h-10 leading-10 text-center',
                                        style: 'margin-top:3px',
                                        onClick: (e) => {
                                            // 阻止事件冒泡，避免触发行点击事件
                                            e.stopPropagation()
                                        },
                                    },
                                    [
                                        h(iconSvg, {
                                            name: 'arrowdrop',
                                            class: 'w-3.5 h-3.5 text-80 dark:text-80-dark',
                                        }),
                                    ]
                                ),
                            ],
                            dropdown: () =>
                                h(
                                    ElDropdownMenu,
                                    {},
                                    {
                                        default: () => [
                                            h(
                                                ElDropdownItem,
                                                { command: 'bindDevice' },
                                                {
                                                    default: () => [
                                                        h(
                                                            ElIcon,
                                                            { class: 'mr-2' },
                                                            {
                                                                default: () =>
                                                                    h(Link),
                                                            }
                                                        ),
                                                        t('Bind Devices'),
                                                    ],
                                                }
                                            ),
                                            h(
                                                ElDropdownItem,
                                                { command: 'viewQRCode' },
                                                {
                                                    default: () => [
                                                        h(
                                                            ElIcon,
                                                            { class: 'mr-2' },
                                                            {
                                                                default: () =>
                                                                    h(View),
                                                            }
                                                        ),
                                                        t('Look QR Code'),
                                                    ],
                                                }
                                            ),
                                            h(
                                                ElDropdownItem,
                                                {
                                                    command: 'deleteDevice',
                                                    disabled:
                                                        !!rowData.customerId ||
                                                        rowData.activeStatus !=
                                                            0,
                                                },
                                                {
                                                    default: () => [
                                                        h(
                                                            ElIcon,
                                                            { class: 'mr-2' },
                                                            {
                                                                default: () =>
                                                                    h(Delete),
                                                            }
                                                        ),
                                                        t('Delete Device'),
                                                    ],
                                                }
                                            ),
                                        ],
                                    }
                                ),
                        }
                    ),
                ]
            )
        },
    },
])
const checkedColumns = ref(columnList.value.map((col) => col.key))
const selectedColumns = ref([
    'sn',
    'hwid',
    'vehicleType',
    'model',
    'status',
    'signal4g',
    'lastHeartbeatTime',
    'address',
    'alarmNub',
    'chgCapSum',
    'lastDischargeTime',
    'cycleCount',
    'onlinePercent',
    'Operation',
])
const dialogVisible = ref(false)
const draggabledColumns = ref(columnList.value)
// 计算可见的列
function getResultArr(arr, rulesArr) {
    const map = arr.reduce((acc, item) => {
        acc[item.key] = item
        return acc
    }, {})
    return rulesArr.map((key) => map[key]).filter((item) => item !== undefined) // 过滤掉不存在的项
}
const visibleColumns = computed(() => {
    return getResultArr(columnList.value, selectedColumns.value)
})
const onChangeCheckedColumns = (e) => {
    // 确保操作列始终被选中
    if (!e.includes('Operation')) {
        e.push('Operation')
    }

    draggabledColumns.value = columnList.value.filter((item) => {
        return checkedColumns.value.includes(item.key)
    })
    if (e.length == columnList.value.length) {
        checkAll.value = true
        isIndeterminate.value = false
    } else if (e.length == 0) {
        checkAll.value = false
        isIndeterminate.value = false
    } else {
        isIndeterminate.value = true
    }
}

// 显示列选择器
const showColumnSelector = () => {
    checkedColumns.value = visibleColumns.value.map((col) => col.key)
    draggabledColumns.value = visibleColumns.value.map((col) => col)
    dialogVisible.value = true
}
// 定义排序状态枚举
const SortOrder = {
    NONE: undefined,
    ASC: 'asc',
    DESC: 'desc',
}

// 获取下一个排序状态
const getNextSortOrder = (currentOrder) => {
    // 如果当前状态是 undefined，则切换到 'asc'
    if (!currentOrder) {
        return SortOrder.ASC
    }
    // 如果当前状态是 'asc'，则切换到 'desc'
    if (currentOrder === SortOrder.ASC) {
        return SortOrder.DESC
    }
    // 如果当前状态是 'desc'，则切换到 undefined
    if (currentOrder === SortOrder.DESC) {
        return SortOrder.NONE
    }
    // 默认返回 undefined
    return SortOrder.NONE
}

// 处理排序变化
const sortBy = ref({
    sortField: undefined,
    sortOrder: undefined,
})

const handleSortChange = ({ key, order }) => {
    // 重置其他列的排序状态为默认
    Object.keys(sortState.value).forEach((k) => {
        if (k !== key) {
            sortState.value[k] = SortOrder.NONE
        }
    })

    // 获取当前列的排序状态
    const currentOrder = sortState.value[key]

    // 获取下一个排序状态
    const nextOrder = getNextSortOrder(currentOrder)

    // 更新排序状态
    sortState.value[key] = nextOrder

    // 构建排序参数
    sortBy.value =
        nextOrder !== SortOrder.NONE
            ? {
                  sortField: key,
                  sortOrder: nextOrder,
              }
            : {
                  sortField: undefined,
                  sortOrder: undefined,
              }

    // 更新请求参数并重新获取数据
    getDeviceData()
}

// 修改所有可排序列的 headerCellRenderer，添加默认状态图标
const getSortIcon = (columnKey, sortState) => {
    const currentOrder = sortState[columnKey]
    // 默认状态（undefined）显示 sort 图标
    if (!currentOrder) {
        return h(iconSvg, {
            name: 'sort',
            class: 'w-3.5 h-3.5 opacity-40',
        })
    }
    // 升序状态显示 asc 图标
    if (currentOrder === SortOrder.ASC) {
        return h(iconSvg, {
            name: 'asc',
            class: 'w-3.5 h-3.5',
        })
    }
    // 降序状态显示 desc 图标
    return h(iconSvg, {
        name: 'desc',
        class: 'w-3.5 h-3.5',
    })
}

// 处理拖拽结束
const handleDragEnd = ({
    newDraggableIndex,
    newIndex,
    oldDraggableIndex,
    oldIndex,
}) => {
    // 可以在这里处理拖拽结束后的逻辑
}
const checkMove = (e) => {
    // 获取拖拽后的目标索引
    const targetIndex = e.draggedContext.futureIndex
    // 如果目标位置是前两个，则禁止拖拽
    return targetIndex >= 2
}
const closeDrawer = () => {
    dialogVisible.value = false
}

const onSave = () => {
    selectedColumns.value = draggabledColumns.value.map((col) => col.key)
    // 确保操作列始终在最后
    if (!selectedColumns.value.includes('Operation')) {
        selectedColumns.value.push('Operation')
    } else {
        // 如果操作列已存在，将其移到最后
        const operationIndex = selectedColumns.value.indexOf('Operation')
        if (operationIndex > -1) {
            selectedColumns.value.splice(operationIndex, 1)
            selectedColumns.value.push('Operation')
        }
    }
    ElMessage.success(t('Successed'))
    dialogVisible.value = false
}
const checkAll = ref(true)
const isIndeterminate = ref(true)
const handleCheckAllChange = (val) => {
    checkedColumns.value = val
        ? columnList.value.map((col) => col.key)
        : ['sn', 'hwid', 'Operation']
    draggabledColumns.value = columnList.value.filter((item) => {
        return checkedColumns.value.includes(item.key)
    })
    isIndeterminate.value = false
}
const onInvert = () => {
    const requiredColumns = ['sn', 'hwid']
    const currentChecked = [...checkedColumns.value]
    const allColumns = columnList.value.map((col) => col.key)

    // 反选逻辑：保留必选项，其他项取反
    checkedColumns.value = [
        ...requiredColumns,
        ...allColumns.filter(
            (key) =>
                !requiredColumns.includes(key) && !currentChecked.includes(key)
        ),
    ]

    // 更新拖拽列表
    draggabledColumns.value = columnList.value.filter((item) => {
        return checkedColumns.value.includes(item.key)
    })
}

// 添加设备
const dialogFormVisible = ref(false)
const formRef = ref(null)
const formData = reactive({
    projectNo: '',
    batteryList: [
        {
            batteryNo: '',
            sn: '',
        },
    ],
})

// 项目列表数据
const projectPageInfo = ref({
    current: 1,
    size: 1000,
})
const projectData = ref([])
const getProjectData = async () => {
    let params = {
        ...projectPageInfo.value,
    }
    let res = await powerApi.getProjectPageList(params)
    projectData.value = res.data.data.records
}

// 添加设备
const addDevice = async () => {
    if (projectData.value.length) {
        //
    } else {
        await getProjectData()
    }
    dialogFormVisible.value = true
}
const cancelAdd = () => {
    dialogFormVisible.value = false
    formRef.value.clearValidate()
    formRef.value.resetFields()
    formData.batteryList = [
        {
            batteryNo: '',
            sn: '',
        },
    ]
}
// 添加电池行
const addBatteryRow = () => {
    formData.batteryList.push({
        batteryNo: '',
        sn: '',
    })
}
//复制行
const copyBatteryRow = (item, index) => {
    formData.batteryList.splice(index + 1, 0, {
        batteryNo: formData.batteryList[index].batteryNo,
        sn: formData.batteryList[index].sn,
    })
}

// 删除电池行
const removeBatteryRow = (index) => {
    formData.batteryList.splice(index, 1)
}

const hasUniqueIds = (arr, type) => {
    const seenIds = new Set()
    for (const item of arr) {
        if (seenIds.has(item[type])) {
            return false
        }
        seenIds.add(item[type])
    }
    return true
}
// 提交表单
const addLoading = ref(false)
const confirmAdd = async () => {
    if (!formRef.value) return
    // 检查设备编号是否重复
    let hasRepeatSn = hasUniqueIds(formData.batteryList, 'sn')
    let hasRepeatBatteryNo = hasUniqueIds(formData.batteryList, 'batteryNo')
    if (formData.batteryList.some((item) => !item.batteryNo)) {
        addLoading.value = false
        ElMessage.error(t('tianjiashebei_tips10'))
        return
    } else if (!hasRepeatBatteryNo) {
        addLoading.value = false
        ElMessage.error(t('tianjiashebei_tips11'))
        return
    } else if (formData.batteryList.some((item) => !item.sn)) {
        addLoading.value = false
        ElMessage.error(t('tianjiashebei_tips02'))
        return
    } else if (!hasRepeatSn) {
        addLoading.value = false
        ElMessage.error(t('tianjiashebei_tips03'))
        return
    }

    formRef.value.validate().then(async () => {
        let params = {
            ...toRaw(formData),
        }

        await powerApi.batchAddBattery(params)
        // return
        // 提交成功后关闭抽屉
        // 刷新数据
        ElMessage.success(t('Successed'))
        await getDeviceData()
        cancelAdd()
    })
}

const emit = defineEmits(['search'])
const searchValueD = ref('')

const importVisible = ref(false)
const addDeviceImport = () => {
    //
    importVisible.value = true
}
const downloadTemp = () => {
    //
    window.open(
        'https://battery-monitor.oss-cn-shanghai.aliyuncs.com/frontend/excelTemplete/%E5%AF%BC%E5%85%A5%E8%AE%BE%E5%A4%87%E6%A8%A1%E6%9D%BF.xlsx',
        '_blank'
    )
}
const cancelImport = () => {
    //
    importVisible.value = false
}

const selectedFile = ref(null)

const handleFileChange = (file) => {
    // 文件类型验证
    const allowedTypes = [
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    ]
    const isExcel = allowedTypes.includes(file.raw.type)
    const isLt5M = file.raw.size / 1024 / 1024 < 5

    if (!isExcel) {
        ElMessage.error(t('Only Excel files can be uploaded'))
        return
    }
    if (!isLt5M) {
        ElMessage.error(t('upload_tips001') + '5MB!')
        return
    }
    selectedFile.value = file.raw
}
const uploadRef = ref()
const handleExceed = (files) => {
    uploadRef.value?.clearFiles()
    const file = files[0]
    file.uid = genFileId()
    uploadRef.value?.handleStart(file)
}
const confirmImport = async () => {
    if (!selectedFile.value) {
        ElMessage.warning(t('Please upload the file first'))
        return
    }
    try {
        const formData = new FormData()
        formData.append('file', selectedFile.value)
        await powerApi.batchImportBattery(formData)
        ElMessage.success(t('Successed'))
        importVisible.value = false
        selectedFile.value = undefined
        await getDeviceData()
        // 可以在这里添加刷新列表的逻辑
    } catch (error) {
        ElMessage.error(t('Password_tips09'), error)
    }
}
const rowClick = ({ rowData }) => {
    router.push({
        name: 'equipmentDetail',
        query: {
            sn: rowData.sn,
            projectId: rowData.projectId,
        },
    })
}
const filterDrawerVisible = ref(false)
const onCloseFilterDrawer = () => {
    //
    filterDrawerVisible.value = false
}
const projectList = ref()
const getProjectList = async (flag) => {
    let params = {
        keyword: projectKeywords.value,
    }
    let res = await powerApi.getProjectAndBatteryCounts(params)
    projectList.value = res.data.data
    if (!flag) {
        checkedProject.value = []
    }
}
const convertToTree = (data) => {
    return data.provinces.map((province) => {
        return {
            label: province.province,
            value: province.province,
            children: province.cities.map((city) => {
                return {
                    label: city.city,
                    value: city.city,
                    children: [], // 目前 city 层没有更深的子节点
                }
            }),
        }
    })
}

const cityOptions = ref([])
const hasCities = ref(false)
const getReginDeviceTree = async () => {
    //
    let res = await powerApi.getReginDeviceTree()
    hasCities.value = true
    cityOptions.value =
        res.data.data && res.data.data.provinces
            ? convertToTree(res.data.data)
            : []
}
const openFilter = async () => {
    getReginDeviceTree()
    filterDrawerVisible.value = true
}
const drawerObject = ref()
const onFilterDrawerSave = async () => {
    drawerObject.value = {
        projectIds: checkedProject.value,
        customerIds: checkedCustomers.value,
        model: formState.model || undefined,
        city: formState.cities,
        activeStartDate: formState.createTimeRange
            ? formState.createTimeRange[0]
            : undefined,
        activeEndDate: formState.createTimeRange
            ? formState.createTimeRange[1]
            : undefined,
    }
    filterFlag.value = true
    await getDeviceData(true)
    filterDrawerVisible.value = false
}

const onFilterReset = async () => {
    filterFlag.value = false
    await getProjectList()
    formState.cities = void 0
    formState.model = void 0
    formState.createTimeRange = []
    checkedCustomers.value = []
    customerTreeRef.value?.setCheckedKeys([], false)
}
const activeFilterName = ref('project')
const handleFilterTabChange = async (name) => {
    if (
        name === 'customer' &&
        (!treeData.value || treeData.value.length === 0)
    ) {
        await getTreeData()
    }
}
const projectKeywords = ref('')
const checkedProject = ref()
const onChangeCheckedProject = (e) => {
    //
    if (e.length == projectList.value.length) {
        checkAllFilter.value = true
        isIndeterminateFilter.value = false
    } else if (e.length == 0) {
        checkAllFilter.value = false
        isIndeterminateFilter.value = false
    } else {
        isIndeterminateFilter.value = true
    }
}
const checkAllFilter = ref(false)
const isIndeterminateFilter = ref(false)

const handleCheckAllChangeFilter = (val) => {
    checkedProject.value = val ? projectList.value.map((col) => col.id) : []
    isIndeterminateFilter.value = false
}
const onSearchProject = () => {
    getProjectList(true)
}
const devicePageInfo = ref({
    current: 1,
    size: 10,
})
const devicePageTotal = ref(0)
const filterFlag = ref(false)
const getDeviceData = async () => {
    const { sortField, sortOrder } = sortBy.value
    let params
    if (filterFlag.value) {
        params = {
            status: statusFilter.value || undefined,
            keyword: searchValueD.value || undefined,
            model: modelFilter.value || undefined,
            vehicleType: vehicleTypeFilter.value || undefined,
            activeStatus: activeStatusFilter.value || undefined,
            serviceStatus: serviceStatusFilter.value || undefined,
            ...devicePageInfo.value,
            ...drawerObject.value,
            sortField,
            sortOrder,
        }
    } else {
        params = {
            status: statusFilter.value || undefined,
            keyword: searchValueD.value || undefined,
            model: modelFilter.value || undefined,
            vehicleType: vehicleTypeFilter.value || undefined,
            activeStatus: activeStatusFilter.value || undefined,
            serviceStatus: serviceStatusFilter.value || undefined,
            ...devicePageInfo.value,
            sortField,
            sortOrder,
        }
    }
    let res = await powerApi.getDevicePageList(params)
    tableData.value = res.data.data.records
    devicePageTotal.value = res.data.data.total
}
const devicePageChange = async () => {
    await getDeviceData(false, undefined)
}
const handleDeviceSizeChange = async (e) => {
    devicePageInfo.value.size = e
    await getDeviceData(false, undefined)
}

const onSearchD = async () => {
    await getDeviceData(false, undefined)
}
onMounted(async () => {
    await store.dispatch('dictionary/getDictionary', 'vehicleType')
    await store.dispatch('dictionary/getDictionary', 'powerBmsModel')
    await getDeviceData()
    await getProjectList()
    // 初始化时获取企业树形数据
    await getTreeData()
})
const customKeywords = ref('')
const onSearchCustom = () => {
    //
}
const formatterActiveTime = (e) => {
    return e.activeTime ? dayjs(e.activeTime).format('YYYY-MM-DD') : ''
}
const vehicleTypes = computed(
    () => store.state.dictionary.dictionaries.vehicleType || []
)
const getVehicleType = (val) => {
    if (vehicleTypes.value.length == 0) return
    if (!val) return '-'
    return vehicleTypes.value.find((item) => item.value == val).label
}

const formatterStatus = (e) => {
    if (e.status != undefined && e.status == null) return '-'
    if (e.status == 3 && e.lastHeartbeatTime) {
        let end = dayjs(new Date())
        let start = dayjs(e.lastHeartbeatTime)
        const preciseHoursDiff = (end.diff(start) / (1000 * 60 * 60)).toFixed(2)
        return (
            t(getState(e.status, 'power').label) + '(' + preciseHoursDiff + 'h)'
        )
    } else {
        return t(getState(e.status, 'power').label)
    }
    // 计算精确的小时差（含小数）
}
const formatterServiceStatus = (e) => {
    return e == 1 ? t('status_zhengchang') : t('Expired')
}

const deviceOptions = computed(
    () => store.state.dictionary.dictionaries.powerBmsModel || []
)

const labelWidth = computed(() => {
    let res =
        locale.value == 'zh' ? '70px' : locale.value == 'en' ? '120px' : '120px'
    return res
})
const itemLabelWidth = computed(() => {
    let res =
        locale.value == 'zh' ? '84px' : locale.value == 'en' ? '140px' : '140px'
    return res
})
const rules = ref()
const formState = reactive({
    model: [],
    cities: [],
    createTimeRange: [undefined, undefined],
})

const shortcuts = ref([
    {
        text: t('Last Week'),
        value: () => {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            return [start, end]
        },
    },
    {
        text: t('Last 30 Days'),
        value: () => {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            return [start, end]
        },
    },
    {
        text: t('Last Year'),
        value: () => {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 366)
            return [start, end]
        },
    },
])
const isName = ref(false)
const changeView = () => {
    isName.value = !isName.value
}
const onDeleteItem = (item) => {
    const index = draggabledColumns.value.findIndex(
        (col) => col.key === item.key
    )
    if (index < 2) {
        return
    }
    if (index > -1) {
        draggabledColumns.value.splice(index, 1)
        const checkedIndex = checkedColumns.value.indexOf(item.key)
        if (checkedIndex > -1) {
            checkedColumns.value.splice(checkedIndex, 1)
        }
    }
}

// 在 script setup 部分添加新的状态变量
const modelFilter = ref('')
const selectedModels = ref([])
const vehicleTypeFilter = ref([])
const selectedVehicleTypes = ref([])
const activeStatusFilter = ref(null)
const selectedActiveStatus = ref(null)
const statusFilter = ref([])
const selectedStatus = ref([])
const serviceStatusFilter = ref(null)
const selectedServiceStatus = ref(null)

const sortState = ref({
    activeTime: SortOrder.NONE,
    signal4g: SortOrder.NONE,
    lastHeartbeatTime: SortOrder.NONE,
    soc: SortOrder.NONE,
    soh: SortOrder.NONE,
    alarmNub: SortOrder.NONE,
    chgTimeSum: SortOrder.NONE,
    chgCapSum: SortOrder.NONE,
    dsgTimeSum: SortOrder.NONE,
    dsgCapSum: SortOrder.NONE,
    lastChargeTime: SortOrder.NONE,
    lastDischargeTime: SortOrder.NONE,
    cycleCount: SortOrder.NONE,
    serviceExpireDate: SortOrder.NONE,
})

// --- Operation Column Logic ---
/**
 * @description: 设备绑定抽屉的可见性
 */
const bindDeviceVisible = ref(false)
/**
 * @description: 查看二维码抽屉的可见性
 */
const viewQRVisible = ref(false)
/**
 * @description: 当前操作的设备数据
 */
const currentDevice = ref(null)
/**
 * @description: 绑定抽屉的模式 ('view' or 'edit')
 */
const bindingMode = ref('view') // 'view' or 'edit'
/**
 * @description: 企业列表，用于绑定设备
 */
const companyList = ref([])
/**
 * @description: 绑定表单的数据模型
 */
const bindingForm = reactive({
    companyId: '',
})
/**
 * @description: Vue QR组件的引用
 */
const qrCodeRef = ref(null)

/**
 * @description: 计算当前设备的二维码URL
 */
const qrCodeUrl = computed(() => {
    // return 'https://ming-enterprise.oss-cn-hangzhou.aliyuncs.com/power-battery/qrcode/sn12345678.png?Expires=4904249194&OSSAccessKeyId=LTAI5t8cF26nCFFt1vesUoFx&Signature=PtBALTPp0aO98dg8Z3%2FB%2Bf0uP04%3D'
    if (currentDevice.value && currentDevice.value.qrCodeUrl) {
        return currentDevice.value.qrCodeUrl
    } else {
        return ''
    }
})

/**
 * @description: 获取企业树结构数据
 */
const fetchCompanyList = async () => {
    try {
        await getTreeData()
        // 将树形数据转换为扁平列表（如果需要保持原有逻辑兼容）
        companyList.value = flattenTreeData(treeData.value)
    } catch (error) {
        ElMessage.error('获取企业列表失败')
    }
}

/**
 * @description: 将树形数据转换为扁平结构
 */
const flattenTreeData = (treeData) => {
    const result = []
    const traverse = (nodes) => {
        nodes.forEach((node) => {
            result.push({
                id: node.id,
                name: node.name,
                label: node.name,
                value: node.id,
            })
            if (node.children && node.children.length > 0) {
                traverse(node.children)
            }
        })
    }
    traverse(treeData)
    return result
}

/**
 * @description: 处理操作列下拉菜单的命令
 * @param {string} command - 命令标识
 * @param {object} rowData - 当前行数据
 */
const handleOperation = async (command, rowData) => {
    currentDevice.value = rowData
    switch (command) {
        case 'bindDevice':
            await fetchCompanyList()
            bindingForm.companyId = rowData.customerId
            bindingMode.value = rowData.customerId ? 'view' : 'edit'
            bindDeviceVisible.value = true
            break
        case 'viewQRCode':
            viewQRVisible.value = true
            break
        case 'deleteDevice':
            ElMessageBox.confirm(
                `${t('确认删除该设备：')}${rowData.sn}`,
                t('确认删除'),
                {
                    confirmButtonText: t('Confirm'),
                    cancelButtonText: t('Cancle'),
                    type: 'warning',
                }
            )
                .then(async () => {
                    try {
                        let res = await powerApi.delBms({ id: rowData.id })
                        if (res.data.data) {
                            ElMessage.success(
                                `${t('设备:')}${rowData.sn}${t('已删除')}`
                            )
                        }
                        await getDeviceData()
                    } catch (error) {
                        ElMessage.error('删除失败')
                    }
                })
                .catch(() => {})
            break
    }
}

/**
 * @description: 关闭设备绑定抽屉并重置状态
 */
const closeBindDrawer = () => {
    bindDeviceVisible.value = false
    currentDevice.value = null
    showDiv.value = false
}

/**
 * @description: 💥 保存设备绑定信息（包含测试用逻辑）
 */
const saveBinding = async () => {
    if (!bindingForm.companyId) {
        return ElMessage.warning('请选择要绑定的企业')
    }
    try {
        // await powerApi.bindDevice({
        //     deviceId: currentDevice.value.id,
        //     customerId: bindingForm.companyId,
        // })
        const params = {
            bmsIds: [currentDevice.value.id],
            customerId: bindingForm.companyId,
        }

        // TODO: 调用批量绑定API
        let res = await powerApi.batchBindCustomer(params)
        if (res.data.code == 0 && res.data.data) {
            ElMessage.success(`成功绑定 ${params.bmsIds.length} 台设备`)
        }
        closeBindDrawer()
        await getDeviceData()
    } catch (error) {
        ElMessage.error('绑定失败')
    }
}

/**
 * @description: 下载二维码图片
 */

const downloadQRCode = () => {
    window.open(qrCodeUrl.value, '_blank')
}

const test = () => {}

const batchBindVisible = ref(false)
const deviceSelectorRef = ref(null)
const batchBindForm = reactive({
    companyId: '',
    deviceIds: [],
})

const openBatchBindDrawer = async () => {
    // 清空表单数据
    batchBindForm.companyId = ''
    batchBindForm.deviceIds = []

    await fetchCompanyList()
    batchBindVisible.value = true

    // 等待下一个 tick，确保组件已经渲染
    await nextTick()

    // 重置设备选择器的筛选条件
    if (deviceSelectorRef.value) {
        deviceSelectorRef.value.resetFilters()
    }
}

const closeBatchBindDrawer = () => {
    batchBindVisible.value = false
    batchBindForm.companyId = ''
    batchBindForm.deviceIds = []
}

const confirmBatchBind = async () => {
    // 验证是否选择了企业
    if (!batchBindForm.companyId) {
        ElMessage.warning(t('请选择要绑定的企业'))
        return
    }

    // 验证是否选择了设备
    if (!batchBindForm.deviceIds || batchBindForm.deviceIds.length === 0) {
        ElMessage.warning(t('请选择要绑定的设备'))
        return
    }

    try {
        // 构造API所需的数据格式
        const params = {
            bmsIds: batchBindForm.deviceIds,
            customerId: batchBindForm.companyId,
        }

        // TODO: 调用批量绑定API
        let res = await powerApi.batchBindCustomer(params)
        if (res.data.code == 0 && res.data.data) {
            ElMessage.success(`成功绑定 ${params.bmsIds.length} 台设备`)
        }

        // 关闭抽屉并重置表单
        closeBatchBindDrawer()

        // 刷新设备列表
        await getDeviceData()
    } catch (error) {
        console.error('批量绑定失败:', error)
        // ElMessage.error('批量绑定失败，请重试')
    }
}
/**
 * @description: 企业树形数据
 */
const treeData = ref([])

/**
 * @description: 获取企业树形结构数据
 */
const getTreeData = async () => {
    const getCompanyInfo = computed(() => store.getters['user/getUserInfoData'])
    try {
        const {
            data: { data, code },
        } = await apiService.getDeviceTree({
            supplierId: getCompanyInfo?.value?.orgId,
            businessType: 'vehicle_battery',
        })
        if (code === 0) {
            // 递归处理节点，确保每个节点都有label和value属性
            const processTreeNodes = (nodes) => {
                if (!nodes || !Array.isArray(nodes)) return []
                return nodes.map((node) => ({
                    ...node,
                    label: node.name || node.label,
                    value: node.id || node.value,
                    children:
                        node.children && node.children.length > 0
                            ? processTreeNodes(node.children)
                            : undefined,
                }))
            }

            const tree = [
                {
                    name: getCompanyInfo?.value?.orgName,
                    id: getCompanyInfo?.value?.orgId,
                    label: getCompanyInfo?.value?.orgName,
                    value: getCompanyInfo?.value?.orgId,
                    children: processTreeNodes(data || []),
                },
            ]

            treeData.value = tree
        }
    } catch (error) {
        console.error('获取企业树形数据失败:', error)
        ElMessage.error('获取企业数据失败')
        // 设置默认数据以避免错误
        treeData.value = [
            {
                name: '默认企业',
                id: 'default',
                label: '默认企业',
                value: 'default',
                children: [],
            },
        ]
    }
}
const isOperator = computed(() => {
    return store.state.user.userInfoData.roles.includes('operation_staff')
    // return false
})
const exportExcel = (data, headers, fileName) => {
    // 此处当返回json文件时需要先对data进行JSON.stringify处理，其他类型文件不用做处理
    const blob = new Blob([data], {
        type: headers['content-type'],
    })
    let dom = document.createElement('a')
    let url = window.URL.createObjectURL(blob)
    dom.href = url
    dom.download = decodeURI(fileName)
    dom.style.display = 'none'
    document.body.appendChild(dom)
    dom.click()
    dom.parentNode.removeChild(dom)
    window.URL.revokeObjectURL(url)
}
// 批量下载二维码的loading状态
const downloadQRLoading = ref(false)

const BatchDownloadQRCode = async () => {
    // if (downloadQRLoading.value) return // 防止重复点击

    downloadQRLoading.value = true
    try {
        const bmsIds = tableData.value.map((item) => {
            return item.id
        })
        let res = await powerApi.batchDownloadQrCode({
            bmsIds,
        })
        const { data, headers } = res
        const fileName = 'QRcodes.zip'
        exportExcel(data, headers, fileName)
        ElMessage.success(t('下载成功'))
    } catch (error) {
        console.error('批量下载二维码失败:', error)
        ElMessage.error(t('下载失败，请重试'))
    } finally {
        setTimeout(() => {
            downloadQRLoading.value = false
        }, 1500)
    }
}

const customerTreeRef = ref(null)
const checkedCustomers = ref([])
const handleCustomerCheck = (data, { checkedKeys }) => {
    checkedCustomers.value = checkedKeys
}
const showDiv = ref(false)
const unBindCustomer = async (id) => {
    let res = await powerApi.unBindCustomer({ id })
    if (res.data.data) {
        ElMessage.success('Success!')
    } else {
        // ElMessage.error('Error!')
    }
}
</script>

<style scoped lang="less">
.custom-table {
    width: 100%;
    height: 100%;
    // position: relative;
    // .table-header {
    //     position: absolute;
    //     width: 100%;
    //     left: 0;
    //     top: -32px;
    // }
    height: calc(100vh - 200px);
    position: relative;
}

.column-selector {
    max-height: calc(100vh - 108px);
    overflow-y: auto;
}
.column-draggable {
    max-height: calc(100vh - 76px);
    overflow-y: auto;
}
.column-item {
    display: flex;
    align-items: center;
    padding: 8px;
    border-bottom: 1px solid #eee;
}

.drag-handle {
    cursor: move;
    margin-right: 8px;
    color: #909399;
    width: 24px;
    height: 24px;
    font-size: 24px;
}
.drag-handle-disabled {
    cursor: not-allowed;
    margin-right: 8px;
    color: #909399;
    width: 24px;
    height: 24px;
    font-size: 24px;
    opacity: 0.4;
}

:deep(.el-checkbox) {
    margin-right: 0;
    width: 100%;
}

:deep(.el-checkbox.is-disabled) {
    cursor: not-allowed;
}
:deep(.el-form-item) {
    margin-bottom: 12px;
}
.project-filter {
    height: calc(100vh - 268px);
    display: flex;
    flex-direction: column;
}
.filter-other {
    height: 140px;
}
:deep(.el-upload-dragger) {
    padding: 20px 10px;
    .el-icon--upload {
        margin-bottom: 8px;
    }
}
:deep(.el-upload-dragger) {
    background-color: rgba(255, 255, 255, 0.2);
}
.el-upload__text {
    color: var(--themeColor);
}

.delete-mask {
    z-index: 99;
    background-color: rgba(255, 255, 255, 0.01);
    backdrop-filter: blur(10px);
    display: none;
}
.file-box {
    &:hover {
        .file-text {
            opacity: 0.2;
        }
        .delete-mask {
            display: block;
        }
    }
}
:deep(.el-dropdown) {
    .el-button-group > .el-button:first-child {
        background-color: transparent !important;
        // color: var(--themeColor) !important;
        border-top-left-radius: 999px;
        border-bottom-left-radius: 999px;
        &:hover {
            background-color: var(--themeColor) !important;
            color: #fff !important;
        }
    }
    .el-button-group .el-button:last-child {
        background-color: transparent !important;
        // color: var(--themeColor) !important;
        border-top-right-radius: 999px;
        border-bottom-right-radius: 999px;
        &:hover {
            background-color: var(--themeColor) !important;
            color: #fff !important;
        }
    }
    .el-dropdown__caret-button {
        width: 40px;
        padding-right: 4px;
    }
}
:deep(.el-table-v2__header-cell .el-icon) {
    display: block;
}
:deep(.hasFilter) {
    color: var(--themeColor);
}
:deep(.el-table-v2__header-cell) {
    .el-table-v2__sort-icon {
        display: none !important;
    }

    &.is-sortable {
        .el-table-v2__header-cell-content {
            position: relative;
            padding-right: 24px;

            &::after {
                content: '';
                position: absolute;
                right: 0;
                top: 50%;
                transform: translateY(-50%);
                width: 16px;
                height: 16px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }

        &.ascending .el-table-v2__header-cell-content::after {
            content: '';
            background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8l-6 6 1.41 1.41L12 10.83l4.59 4.58L18 14z"/></svg>')
                no-repeat center;
            background-size: contain;
        }

        &.descending .el-table-v2__header-cell-content::after {
            content: '';
            background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"/></svg>')
                no-repeat center;
            background-size: contain;
        }
    }
}
:deep(.el-button:focus-visible) {
    outline: none !important;
}
.dark {
    :deep(.el-tree-node__content) {
        &:hover {
            background-color: rgba(255, 255, 255, 0.1) !important;
        }
    }
    :deep(.el-tree-node:focus > .el-tree-node__content) {
        background-color: rgba(255, 255, 255, 0.1) !important;
    }
}
:deep(.el-tree-node__content .el-checkbox) {
    margin-right: 8px;
    width: auto;
}
// .qrcode-text {
//     color: var(--themeColor);
// }
// .element-plus-tree {
//     :deep(.el-tree) {
//         .el-tree-node {
//             border-left: 1px solid var(--border);
//         }
//     }
// }
</style>
