<template>
    <div>
        <div class="flex justify-between items-center mb-3">
            <div class="leading-8 text-title dark:text-title-dark">
                {{
                    type == 'alarm'
                        ? $t('alarm_yichangtongji')
                        : $t('gongdantongji')
                }}
            </div>
            <div class="flex items-center gap-x-3">
                <el-button
                    plain
                    round
                    class="btn-hover"
                    @click="onAdd"
                    v-if="isOperator && route.name == 'operation'"
                >
                    <span>{{ $t('xinzenggongdan') }}</span>
                    <span class="icon-box ml-0.5" style="width: 18px">
                        <iconSvg name="addOrder" class="icon-default" />
                    </span>
                </el-button>
            </div>
        </div>
        <div class="mb-2">
            <div class="echarts-all-box flex gap-x-6">
                <div class="flex-1 charts-box flex items-center">
                    <div id="echarts-1" class="ecahrts-dom"></div>
                    <div class="space-y-1">
                        <div
                            v-for="(item, index) in chartData.chart1Data"
                            :key="index"
                            class="flex items-center text-sm leading-5"
                        >
                            <div
                                class="w-2 h-2 rounded-full mr-1"
                                :style="{
                                    backgroundColor: item.itemStyle.color,
                                }"
                            ></div>
                            <div class="w-20 text-title dark:text-title-dark">
                                {{ item.legendName }}
                            </div>
                            <div class="text-title dark:text-title-dark">
                                {{ item.value }}{{ $t('common_tiao') }}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex-1 charts-box flex items-center">
                    <div id="echarts-2" class="ecahrts-dom"></div>
                    <div class="space-y-1">
                        <div
                            v-for="(item, index) in chartData.chart2Data"
                            :key="index"
                            class="flex items-center text-sm leading-5"
                        >
                            <div
                                class="w-2 h-2 rounded-full mr-1"
                                :style="{
                                    backgroundColor: item.itemStyle.color,
                                }"
                            ></div>
                            <div class="w-20 text-title dark:text-title-dark">
                                {{ item.legendName }}
                            </div>
                            <div class="text-title dark:text-title-dark">
                                {{ item.value }}{{ $t('common_tiao') }}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex-1 charts-box flex items-center">
                    <div id="echarts-3" class="ecahrts-dom"></div>
                    <div class="space-y-1">
                        <div
                            v-for="(item, index) in chartData.chart3Data"
                            :key="index"
                            class="flex items-center text-sm leading-5"
                        >
                            <div
                                class="w-2 h-2 rounded-full mr-1"
                                :style="{
                                    backgroundColor: item.itemStyle.color,
                                }"
                            ></div>
                            <div class="w-20 text-title dark:text-title-dark">
                                {{ item.legendName }}
                            </div>
                            <div class="text-title dark:text-title-dark">
                                {{ item.value }}{{ $t('common_tiao') }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="table rounded-lg w-full">
            <search
                :title="
                    type == 'alarm'
                        ? $t('alarm_yichangliebiao')
                        : $t('gongdanliebiao')
                "
                :searchData="searchData"
                v-model:filter="searchData.filterData.value"
                @search="refresh"
                :showRefresh="false"
                class="teble-search"
            >
            </search>
            <div class="table-box">
                <mw-table
                    :dataSource="tableData"
                    :columns="columns"
                    :hasPage="true"
                    :pageConfig="{ changePage, paginationProps }"
                    :customRow="Rowclick"
                    :rowKey="(record) => record.id"
                    @change="onTableChange"
                    :showRefresh="false"
                    class="alarm-table"
                >
                    <template #deviceDesc="{ record }">
                        <div
                            class="alarm-name font-medium text-primary-text dark:text-title-dark"
                        >
                            {{ record.name || '-' }}
                        </div>
                        <div
                            class="alarm-sn text-secondar-text dark:text-60-dark"
                        >
                            {{
                                type == 'alarm'
                                    ? $t('yichangbianhao')
                                    : $t('gongdanbianhao')
                            }}：{{ record.orderNo }}
                        </div>
                    </template>
                    <template #orderStatus="{ record }">
                        <dictionary
                            :statusOptions="orderStatuss"
                            :value="record.orderStatus"
                            :color="'color'"
                        />
                    </template>
                    <template #processDuration="{ record }">
                        {{ record.processDuration }}{{ $t('common_hour') }}
                    </template>
                    <template #disposeStaffName="{ record }">
                        {{ record.disposeStaffName || '-' }}
                    </template>
                    <template #createTime="{ record }">
                        {{
                            dayjs(record.createTime).format('YYYY-MM-DD HH:mm')
                        }}
                    </template>
                </mw-table>
            </div>
        </div>
        <el-drawer
            v-model="addVisible"
            :size="486"
            :show-close="false"
            :close-on-press-escape="false"
            :destroy-on-close="true"
            @close="cancelAdd"
        >
            <template #header>
                <div
                    class="drawer-header flex items-center justify-between leading-5.5"
                >
                    <div>
                        <span>{{ $t('Add') }}</span>
                    </div>
                    <div>
                        <el-button
                            plain
                            round
                            @click="cancelAdd"
                            class="mr-4"
                            >{{ $t('common_guanbi') }}</el-button
                        >
                        <el-button
                            plain
                            round
                            type="primary"
                            @click="addNewWorkOrder"
                            >{{ $t('tianjia') }}</el-button
                        >
                    </div>
                </div>
            </template>
            <div>
                <el-form
                    ref="ruleFormRef"
                    style="max-width: 600px"
                    :model="createForm"
                    :rules="rules"
                    label-width="80"
                    label-position="left"
                    class="createForm"
                    hide-required-asterisk
                    :status-icon="false"
                >
                    <el-form-item
                        :label="$t('Station Name') + '：'"
                        prop="stationId"
                    >
                        <el-select-v2
                            v-model="createForm.stationId"
                            :placeholder="$t('placeholder_qingxuanze')"
                            :options="stations1"
                            :props="{
                                value: 'id',
                                label: 'stationName',
                            }"
                        />
                    </el-form-item>
                    <el-form-item
                        :label="
                            type == 'alarm'
                                ? $t('alarm_yichangmingcheng') + '：'
                                : $t('gongdanmingcheng') + '：'
                        "
                        prop="name"
                    >
                        <el-input
                            v-model="createForm.name"
                            :placeholder="$t('placeholder_qingshuru')"
                        />
                    </el-form-item>
                    <el-form-item
                        :label="$t('genjinren') + '：'"
                        prop="disposeStaffId"
                    >
                        <el-select-v2
                            v-model="createForm.disposeStaffId"
                            :placeholder="$t('qingxuanzegenjinren')"
                            :options="disposeStaffs"
                        />
                    </el-form-item>
                    <div class="text-secondar-text dark:text-60-dark mb-2">
                        {{ $t('buchongshuoming') }}：
                    </div>
                    <el-form-item label="" prop="problemDescription">
                        <el-input
                            v-model="createForm.problemDescription"
                            type="textarea"
                            :placeholder="$t('textarea_tips01')"
                            maxlength="200"
                            input-style="height: 100px"
                        />
                    </el-form-item>
                </el-form>
            </div>
        </el-drawer>
        <el-drawer
            v-model="detailVisible"
            :size="486"
            :show-close="false"
            :close-on-press-escape="false"
            :destroy-on-close="true"
            @close="onClose"
        >
            <template #header>
                <div
                    class="drawer-header flex items-center justify-between leading-5.5"
                >
                    <div>
                        <span>
                            {{
                                type == 'alarm'
                                    ? $t('yichangxiangqing')
                                    : $t('gongdanxiangqing')
                            }}
                        </span>
                    </div>
                    <div class="flex gap-x-3">
                        <el-button plain round @click="onClose">{{
                            $t('common_guanbi')
                        }}</el-button>
                        <el-popconfirm
                            v-if="detailInfo?.orderStatus !== 2 && isOperator"
                            :title="$t('IsResolved?') + '?'"
                            :confirm-button-text="$t('common_shi')"
                            :cancel-button-text="$t('common_fou')"
                            width="240"
                            @confirm="solveWorkOrder"
                        >
                            <template #reference>
                                <el-button plain round type="primary">{{
                                    $t('jiejue')
                                }}</el-button>
                            </template>
                        </el-popconfirm>
                    </div>
                </div>
            </template>
            <div>
                <div class="list flex items-start">
                    <div class="text-secondar-text dark:text-60-dark w-20">
                        {{
                            type == 'alarm'
                                ? $t('alarm_yichangmingcheng')
                                : $t('gongdanmingcheng')
                        }}：
                    </div>
                    <div class="flex-1 text-title dark:text-title-dark">
                        {{ detailInfo?.name }}
                    </div>
                </div>
                <div class="list flex items-start">
                    <div class="text-secondar-text dark:text-60-dark w-20">
                        {{
                            type == 'alarm'
                                ? $t('yichangbianhao')
                                : $t('gongdanbianhao')
                        }}：
                    </div>
                    <div class="flex-1 text-title dark:text-title-dark">
                        {{ detailInfo?.orderNo }}
                    </div>
                </div>

                <div class="list flex items-start">
                    <div class="text-secondar-text dark:text-60-dark w-20">
                        {{
                            type == 'alarm'
                                ? $t('alarm_yichangzhuangtai')
                                : $t('gongdanzhuangtai')
                        }}
                        ：
                    </div>
                    <div class="flex-1 text-title dark:text-title-dark">
                        <dictionary
                            :statusOptions="orderStatuss"
                            :value="detailInfo?.orderStatus"
                            :color="'backGroundColor'"
                        />
                    </div>
                </div>
                <div class="list flex items-start">
                    <div class="text-secondar-text dark:text-60-dark w-20">
                        {{
                            type == 'alarm'
                                ? $t('alarm_yichangleixing')
                                : $t('gongdanleixing')
                        }}
                        ：
                    </div>
                    <div class="flex-1 text-title dark:text-title-dark">
                        {{ getOrderType(detailInfo?.orderType) }}
                    </div>
                </div>
                <div class="list flex items-start">
                    <div class="text-secondar-text dark:text-60-dark w-20">
                        {{ $t('genjinren') }}：
                    </div>
                    <div class="flex-1 text-title dark:text-title-dark">
                        {{ detailInfo?.disposeStaffName || '-' }}
                    </div>
                </div>
                <div class="list flex items-start">
                    <div class="text-secondar-text dark:text-60-dark w-20">
                        {{ $t('alarm_chulishichang') }}：
                    </div>
                    <div class="flex-1 text-title dark:text-title-dark">
                        {{ detailInfo?.processDuration }}{{ $t('common_hour') }}
                    </div>
                </div>
                <div class="list flex items-start">
                    <div class="text-secondar-text dark:text-60-dark w-20">
                        {{ $t('chuangjianshijian') }}：
                    </div>
                    <div class="flex-1 text-title dark:text-title-dark">
                        {{ detailInfo?.createTime }}
                    </div>
                </div>
                <div class="list" v-if="detailInfo?.orderType != 4">
                    <div class="p-3 drawer-container-bg rounded-lg">
                        <div
                            class="list flex items-start text-secondar-text dark:text-60-dark"
                        >
                            {{ $t('guanliangaojingxinxi') }}
                        </div>
                        <div class="list flex items-start">
                            <div
                                class="text-secondar-text dark:text-60-dark w-20"
                            >
                                {{ $t('alarm_gaojingmingcheng') }}：
                            </div>
                            <div class="flex-1 text-title dark:text-title-dark">
                                {{ detailInfo?.relationAlarm?.alarmDesc }}
                            </div>
                        </div>
                        <div class="list flex items-start">
                            <div
                                class="text-secondar-text dark:text-60-dark w-20"
                            >
                                {{ $t('gaojingbianhao') }}：
                            </div>
                            <div class="flex-1 text-title dark:text-title-dark">
                                {{ detailInfo?.relationAlarm?.deviceSn }}
                            </div>
                        </div>
                        <div class="list flex items-start">
                            <div
                                class="text-secondar-text dark:text-60-dark w-20"
                            >
                                {{ $t('gaojingzhuangtai') }}：
                            </div>
                            <div class="flex-1 text-title dark:text-title-dark">
                                <dictionary
                                    :statusOptions="alarmStatusList"
                                    :value="
                                        detailInfo?.relationAlarm?.alarmStatus
                                    "
                                    :color="'backGroundColor'"
                                />
                            </div>
                        </div>
                        <div class="list flex items-start">
                            <div
                                class="text-secondar-text dark:text-60-dark w-20"
                            >
                                {{ $t('gaojingjibie') }}：
                            </div>
                            <div class="flex-1 text-title dark:text-title-dark">
                                <dictionary
                                    :showBadge="false"
                                    :statusOptions="alarmLevelList"
                                    :value="
                                        detailInfo?.relationAlarm?.alarmLevel
                                    "
                                />
                            </div>
                        </div>
                        <div class="list flex items-start">
                            <div
                                class="text-secondar-text dark:text-60-dark w-20"
                            >
                                {{ $t('Station Name') }}：
                            </div>
                            <div class="flex-1 text-title dark:text-title-dark">
                                {{ detailInfo?.relationAlarm?.stationName }}
                            </div>
                        </div>
                        <div class="list flex items-start">
                            <div
                                class="text-secondar-text dark:text-60-dark w-20"
                            >
                                {{ $t('station_zhandianbianhao') }}：
                            </div>
                            <div class="flex-1 text-title dark:text-title-dark">
                                {{ detailInfo?.relationAlarm?.stationNo }}
                            </div>
                        </div>
                        <div class="list flex items-start">
                            <div
                                class="text-secondar-text dark:text-60-dark w-20"
                            >
                                {{ $t('alarm_jiguibianhao') }}：
                            </div>
                            <div class="flex-1 text-title dark:text-title-dark">
                                {{ detailInfo?.relationAlarm?.containerNo }}
                            </div>
                        </div>
                        <div class="list flex items-start">
                            <div
                                class="text-secondar-text dark:text-60-dark w-20"
                            >
                                {{ $t('Device Type') }}：
                            </div>
                            <div class="flex-1 text-title dark:text-title-dark">
                                {{ detailInfo?.relationAlarm?.deviceType }}
                            </div>
                        </div>
                        <div class="list flex items-start">
                            <div
                                class="text-secondar-text dark:text-60-dark w-20"
                            >
                                {{ $t('Device No') }}：
                            </div>
                            <div class="flex-1 text-title dark:text-title-dark">
                                {{ detailInfo?.relationAlarm?.deviceSn }}
                            </div>
                        </div>
                        <div class="list flex items-start">
                            <div
                                class="text-secondar-text dark:text-60-dark w-20"
                            >
                                {{ $t('gaojing') }}{{ $t('Time') }}：
                            </div>
                            <div class="flex-1 text-title dark:text-title-dark">
                                {{ detailInfo?.relationAlarm?.alarmTime }}
                            </div>
                        </div>
                    </div>
                </div>
                <div v-else>
                    <div class="p-3 drawer-container-bg rounded-lg">
                        <div
                            class="list flex items-start text-secondar-text dark:text-60-dark"
                        >
                            {{ $t('buchongxinxi') }}：
                            {{ detailInfo?.problemDescription }}
                        </div>
                    </div>
                </div>
                <el-form
                    ref="formRef"
                    style="max-width: 600px"
                    :model="formState"
                    :rules="formRules"
                    label-width="auto"
                    class="formRef"
                    hide-required-asterisk
                    :status-icon="false"
                    v-show="isOperator"
                    scroll-to-error
                >
                    <div class="list">
                        <div
                            class="list flex items-start text-secondar-text dark:text-60-dark mt-2"
                        >
                            {{ $t('fashengyuanyin') }}：
                        </div>
                        <div class="mt-2">
                            <el-form-item label="" prop="cause">
                                <el-input
                                    type="textarea"
                                    v-model="formState.cause"
                                    :placeholder="$t('textarea_tips01')"
                                    class="bg-ff dark:bg-ff-dark text-primary-text dark:text-80-dark rounded"
                                    show-word-limit
                                    maxlength="200"
                                    :disabled="detailInfo?.orderStatus == 2"
                                ></el-input>
                            </el-form-item>
                        </div>
                    </div>
                    <div class="list">
                        <div
                            class="list flex items-start text-secondar-text dark:text-60-dark"
                        >
                            {{ $t('weixiujilu') }}：
                        </div>
                        <div class="mt-2">
                            <el-form-item label="" prop="solution">
                                <el-input
                                    type="textarea"
                                    v-model="formState.solution"
                                    :placeholder="$t('weixiujilu_tips01')"
                                    class="bg-ff dark:bg-ff-dark text-primary-text dark:text-80-dark rounded"
                                    show-word-limit
                                    maxlength="200"
                                    :disabled="detailInfo?.orderStatus == 2"
                                ></el-input>
                            </el-form-item>
                        </div>
                    </div>
                    <div class="list flex">
                        <div
                            class="list flex items-start text-secondar-text dark:text-60-dark"
                            v-show="
                                detailInfo?.orderStatus != 2 || fileList.length
                            "
                        >
                            {{
                                detailInfo?.orderStatus != 2
                                    ? $t('Upload Image') + '：'
                                    : $t('tupian_tips') + '：'
                            }}
                        </div>
                        <div class="mt-2 flex-1">
                            <el-upload
                                class="upload-demo"
                                :class="{ hide: hideUpload }"
                                :action="url + '/file/uploadFile'"
                                list-type="picture-card"
                                :on-preview="handlePreview"
                                :on-remove="handleRemove"
                                :multiple="true"
                                :limit="9"
                                :data="getUploadData"
                                :file-list="fileList"
                                :before-upload="beforeUpload"
                                @change="updateFileList"
                                :headers="{
                                    Authorization: `Bearer ${token}`,
                                }"
                                :disabled="detailInfo?.orderStatus == 2"
                            >
                                <template v-if="true">
                                    <el-icon>
                                        <Plus />
                                    </el-icon>
                                </template>
                            </el-upload>
                        </div>
                    </div>
                </el-form>
            </div>
        </el-drawer>
        <el-dialog v-model="dialogVisible">
            <img width="100%" :src="dialogImageUrl" alt="" />
        </el-dialog>
    </div>
</template>

<script setup>
import {
    onMounted,
    ref,
    computed,
    watch,
    reactive,
    onBeforeMount,
    nextTick,
    toRefs,
} from 'vue'
import {
    getPieOption,
    alarmStatusList,
    alarmLevelList,
    chartsColors,
    DateOptionsMap,
    processDurations,
    orderTypes,
    orderStatuss,
    deviceTypeList,
} from '@/views/device/const'
import Search from '@/components/search/index.vue'
import Dictionary from '@/components/table/dictionary.vue'
import apiService from '@/apiService/device'
import api from '@/apiService/strategy'
import * as echarts from 'echarts'
import dayjs from 'dayjs'
import { usePagenation } from '@/common/setup'
import store from '@/store'
import { Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { echartsColorVars, getThemeColor } from '@/common/util'
import useTheme from '@/common/useTheme'
const { t, locale } = useI18n()
const route = useRoute()
const router = useRouter()
const token = store.getters['user/getNewToken']
const isOperator = computed(() => {
    return store.state.user.userInfoData.roles.includes('operation_staff')
})
const props = defineProps({
    title: {
        type: String,
        default: '',
    },
    type: {
        type: String,
        default: 'workorder',
    },
    getAlarmDataFlag: {
        type: Boolean,
        default: false,
    },
    supplierId: {
        type: String,
        default: '',
    },
})
const { getAlarmDataFlag, supplierId } = toRefs(props)
const tableLoading = ref(false)
const formatData = (data, id, name) => {
    const chartDischargeDom = document.getElementById(id)
    chartDischargeDom && echarts.dispose(chartDischargeDom)
    echarts.init(chartDischargeDom).setOption(getPieOption(name, data))
}
const chartData = ref({})
const getName = (arr, val) => {
    return arr.find((item) => item.value == val)
}
const getWorkOrderChartData = async (params) => {
    const { data } = await apiService.getStatisticalSummary(params)

    // 辅助函数，创建图表数据
    const createChartItem = (name, value, color) => ({
        name: `${name}`,
        legendName: name,
        value,
        itemStyle: {
            color,
            borderColor: '#fff',
            borderWidth: 2,
        },
    })

    const statusQuantity = data.data.statusQuantity
    if (!statusQuantity) {
        chartData.value.chart1Data = [
            {
                name: t('alarm_options_chulizhong'),
                legendName: t('alarm_options_chulizhong'),
                value: 0,
                itemStyle: {
                    color: '#FF5D5F',
                    borderColor: '#fff',
                    borderWidth: 2,
                },
            },
            {
                name: t('Resolved'),
                legendName: t('Resolved'),
                value: 0,
                itemStyle: {
                    color: '#8AE6C5',
                    borderColor: '#fff',
                    borderWidth: 2,
                },
            },
        ]
    } else {
        chartData.value.chart1Data = [
            createChartItem(
                t('alarm_options_chulizhong'),
                statusQuantity.processingQuantity,
                '#FF5D5F'
            ),
            createChartItem(
                t('Resolved'),
                statusQuantity.finishedQuantity,
                '#8AE6C5'
            ),
        ]
    }

    const processDurationQuantity = data.data.processDurationQuantity
    if (!processDurationQuantity) {
        chartData.value.chart2Data = [
            createChartItem(t('alarm_options_0_4'), 0, '#EBF3F5'),
            createChartItem(t('alarm_options_4_8'), 0, '#D1E8ED'),
            createChartItem(t('alarm_options_8_24'), 0, '#A9D7E0'),
            createChartItem(t('alarm_options_1_3'), 0, '#92CEDA'),
            createChartItem(t('alarm_options_3more'), 0, '#6FBECE'),
        ]
    } else {
        chartData.value.chart2Data = [
            createChartItem(
                t('alarm_options_0_4'),
                processDurationQuantity.quantity1,
                '#EBF3F5'
            ),
            createChartItem(
                t('alarm_options_4_8'),
                processDurationQuantity.quantity2,
                '#D1E8ED'
            ),
            createChartItem(
                t('alarm_options_8_24'),
                processDurationQuantity.quantity3,
                '#A9D7E0'
            ),
            createChartItem(
                t('alarm_options_1_3'),
                processDurationQuantity.quantity4,
                '#92CEDA'
            ),
            createChartItem(
                t('alarm_options_3more'),
                processDurationQuantity.quantity5,
                '#6FBECE'
            ),
        ]
        console.log(
            '[ chartData.value.chart2Data ] >',
            chartData.value.chart2Data
        )
    }

    // 处理 chart3Data
    if (data.data.disposeStaffQuantity.length) {
        const colors = chartsColors
        const total = data.data.disposeStaffQuantity.reduce(
            (total, item) => total + item.quantity,
            0
        )
        let top3Total = 0
        let otherTotal = 0
        let newData = data.data.disposeStaffQuantity
            .sort((a, b) => b.quantity - a.quantity) // 根据quantity从大到小排序
            .map((item, index) =>
                createChartItem(
                    item.disposeStaffName,
                    item.quantity,
                    colors[index].color
                )
            )
        if (newData.length >= 5) {
            // 计算top3总计
            top3Total = newData[0].value + newData[1].value + newData[2].value
            otherTotal = total - top3Total
            newData = newData
                .slice(0, 3)
                .concat(createChartItem(t('qita'), otherTotal, colors[3].color))
        }
        console.log('[ newData ] >', newData)
        chartData.value.chart3Data = newData
    } else {
        chartData.value.chart3Data = []
    }

    // 异步渲染图表
    nextTick(() => {
        formatData(
            chartData.value.chart1Data,
            'echarts-1',
            props.type == 'alarm'
                ? t('alarm_yichangzhuangtai')
                : t('gongdanzhuangtai')
        )
        formatData(
            chartData.value.chart2Data,
            'echarts-2',
            t('alarm_chulishichang')
        )
        formatData(
            chartData.value.chart3Data,
            'echarts-3',
            t('alarm_chulirenyuan')
        )
    })
}
// 根据路由判读是否展示名称搜索框
const options = computed(() => {
    if (route.path == '/device/deviceDetail') {
        return {}
    } else {
        return {
            stationNo: {
                name: t('Station Name'),
                ELType: 'el-select-v2',
                placeholder: t('Station Name'),
                options: computed(() => {
                    return stations.value
                }),
                optionFilterProp: 'label',
            },
        }
    }
})
const defaultRangeDate = [
    dayjs().subtract(7, 'day').format('YYYY-MM-DD'),
    dayjs().format('YYYY-MM-DD'),
]
const searchData = reactive({
    fields: {
        rangeDate: {
            ELType: 'el-date-picker',
            value: route.path == '/operation' ? defaultRangeDate : [],
            valueFormat: 'YYYY-MM-DD',
            width: '240px',
            // separator: '—',
            allowClear: true,
        },
        orderType: {
            ELType: 'el-select',
            options: [
                { value: '', label: t('alarm_quanbuleixing') },
                ...orderTypes,
            ],
            placeholder:
                props.type == 'alarm'
                    ? t('alarm_yichangleixing')
                    : t('gongdanleixing'),
            value: '',
            width: '150px',
        },
        orderStatus: {
            ELType: 'el-select',
            options: [
                { value: '', label: t('alarm_options_quanbuzhuangtai') },
                ...orderStatuss,
            ],
            placeholder:
                props.type == 'alarm'
                    ? t('alarm_yichangzhuangtai')
                    : t('gongdanzhuangtai'),
            value: 1,
            width: '150px',
        },
        processDurationOption: {
            ELType: 'el-select',
            options: [
                { value: '', label: t('alarm_options_quanbushichang') },
                ...processDurations,
            ],
            placeholder: t('alarm_chulishichang'),
            width: '150px',
        },
        disposeStaffId: {
            ELType: 'el-select',
            placeholder: t('alarm_chulirenyuan'),
            options: [],
            width: '150px',
            // allowClear: true,
        },
    },
    filterData: {
        value: {
            // filterValue: route?.query?.stationName
            //     ? route.query.stationName
            //     : '',
            // filterBy: 'stationName',
        },

        options: options.value,

        config: {
            filterByWidth: '120px',
        },
    },
})

const tableData = ref([])
const columns = [
    {
        title:
            props.type == 'alarm'
                ? t('alarm_yichangmingcheng')
                : t('gongdanmingcheng'),
        dataIndex: 'deviceDesc',
        key: 'deviceDesc',
        slots: {
            customRender: 'deviceDesc',
        },
        width: 360,
    },
    {
        title: t('Station Name'),
        dataIndex: 'stationName',
        key: 'stationName',
        width: 240,
    },
    {
        title: t('alarm_jiguibianhao'),
        dataIndex: 'containerNo',
        key: 'containerNo',
        width: 160,
        align: 'center',
    },
    {
        title:
            props.type == 'alarm'
                ? t('alarm_yichangzhuangtai')
                : t('gongdanzhuangtai'),
        dataIndex: 'orderStatus',
        key: 'orderStatus',
        slots: {
            customRender: 'orderStatus',
        },
        width: 160,
        align: 'center',
    },
    {
        title: t('alarm_chulishichang'),
        dataIndex: 'processDuration',
        key: 'processDuration',
        slots: {
            customRender: 'processDuration',
        },
        width: 160,
        align: 'center',
    },
    {
        title: t('alarm_chulirenyuan'),
        dataIndex: 'disposeStaffName',
        key: 'disposeStaffName',
        slots: {
            customRender: 'disposeStaffName',
        },
        align: 'center',
    },
    {
        title: t('chuangjianshijian'),
        dataIndex: 'createTime',
        key: 'createTime',
        slots: {
            customRender: 'createTime',
        },
        align: 'right',
    },
]

const getWorkOrderTableData = async (params) => {
    //
    let res = await apiService.getWorkOrderPage({
        ...params,
        ...pageParam.value,
    })
    tableData.value = res.data.data.records
    paginationProps.value.total = res.data.data.total
}
const getParams = () => {
    let params = {}
    params.supplierId = supplierId.value || undefined
    for (const key in searchData.fields) {
        params[key] = searchData.fields[key].value
    }
    if (searchData.fields.rangeDate.value) {
        params.startDate = searchData.fields.rangeDate.value[0]
        params.endDate = searchData.fields.rangeDate.value[1]
    }

    params.rangeDate = undefined
    params.stationNo = route.query.stationNo || undefined
    // //单个搜索信息
    params[searchData.filterData.value.filterBy] =
        searchData.filterData.value.filterValue
    return params
}
const { paginationProps, changePage, onTableChange, pageParam } = usePagenation(
    () => {
        getTableData()
    }
)
const { refresh } = usePagenation(() => {
    paginationProps.value.current = 1
    getData()
})
const getTableData = async () => {
    tableLoading.value = true
    let params = getParams()
    await getWorkOrderTableData(params)
    tableLoading.value = false
}

const getData = async () => {
    tableLoading.value = true
    let params = getParams()
    await getWorkOrderChartData(params)
    await getWorkOrderTableData(params)
    tableLoading.value = false
}

const disposeStaffs = ref([])
const getUserNames = async () => {
    let res = await apiService.getStaffByRole({ roleId: 3 })
    let resultData = res.data.data.map((item) => ({
        label: item.name || '',
        value: item.id,
    }))
    searchData.fields.disposeStaffId.options = [
        { value: '', label: t('alarm_quanburenyuan') },
        ...resultData,
    ]
    disposeStaffs.value = resultData
}
// ⬇️ 新建工单
const stations = ref([])
const addVisible = ref(false)
const createForm = reactive({})
const rules = reactive({
    stationId: [
        {
            required: true,
            message: t('placeholder_qingxuanze'),
            trigger: 'change',
        },
    ],
    name: [
        {
            required: true,
            message: t('placeholder_qingshuru'),
            trigger: 'blur',
        },
        {
            min: 2,
            max: 24,
            message: t('chuangdu_tips'.replace('s%', 2).replace('e%', 24)),
            trigger: 'blur',
        },
    ],
    disposeStaffId: [
        {
            required: true,
            message: t('qingxuanzegenjinren'),
            trigger: 'change',
        },
    ],
})

const getCompanyInfo = computed(() => store.getters['user/getUserInfoData'])
const orgId = ref(
    getCompanyInfo?.value?.orgId ? getCompanyInfo.value.orgId : void 0
)
const onAdd = async () => {
    // const res = await api.getOrgAndSubOrgStationNameList({
    //     orgId: orgId.value || '1726907974555729921',
    // })
    // stations.value = res.data.data.map((item) => ({
    //     label: item.stationName,
    //     value: item.id,
    // }))
    addVisible.value = true
}
const ruleFormRef = ref(null)
const cancelAdd = () => {
    addVisible.value = false
    ruleFormRef.value.resetFields()
}

const addNewWorkOrder = async () => {
    //
    ruleFormRef.value.validate(async (valid) => {
        if (!valid) return
        let params = {
            ...createForm,
        }
        let res = await apiService.manualCreate(params)
        if (res.data.data) {
            cancelAdd()
            ElMessage.success(t('Successed'))
            refresh()
        }
    })
}

// ⬆️  新建工单
// ⬇️ 工单详情
const detailVisible = ref(false)
const detailInfo = ref()
const getSpecialWorkDetail = async (id) => {
    detailVisible.value = true
    let res = await apiService.getWorkOrderDetail({ id: id })
    detailInfo.value = res.data.data
    formState.cause = res.data.data.cause || ''
    formState.solution = res.data.data.solution || ''
    fileList.value =
        (res.data.data.files &&
            res.data.data.files.map((item) => {
                return {
                    name: t('tupian'),
                    url: item.fileVisitUrl,
                }
            })) ||
        []
    hideUpload.value = res.data.data.orderStatus == 2
}
defineExpose({ getSpecialWorkDetail })
const Rowclick = (record) => {
    return {
        onClick: async (event) => {
            // 点击行
            detailVisible.value = true
            let res = await apiService.getWorkOrderDetail({ id: record.id })
            detailInfo.value = res.data.data
            formState.cause = res.data.data.cause || ''
            formState.solution = res.data.data.solution || ''
            fileList.value =
                (res.data.data.files &&
                    res.data.data.files.map((item) => {
                        return {
                            name: t('tupian'),
                            url: item.fileVisitUrl,
                        }
                    })) ||
                []
            hideUpload.value = res.data.data.orderStatus == 2
        },
    }
}
const getOrderType = (val) => {
    return orderTypes.find((item) => item.value === val)?.label
}
const formRef = ref(null)
const solveWorkOrder = async () => {
    //
    formRef.value.validate(async (valid) => {
        if (!valid) return
        let params = {
            ...formState,
            id: detailInfo.value.id,
            fileIds: fileList.value.map((item) => item.response.data.fileId),
            orderStatus: 2,
        }
        let res = await apiService.updateWorkOrder(params)
        if (res.data.data) {
            onClose()
            refresh() // 更新列表
        }
    })
}
const formState = reactive({
    cause: undefined,
    solution: undefined,
})
const formRules = {
    cause: [
        {
            required: true,
            message: t('placeholder_qingshuru'),
            trigger: 'blur',
        },
        {
            min: 2,
            max: 200,
            message: t('chuangdu_tips'.replace('s%', 2).replace('e%', 200)),
            trigger: 'blur',
        },
    ],
    solution: [
        {
            required: true,
            message: t('placeholder_qingshuru'),
            trigger: 'blur',
        },
        {
            min: 2,
            max: 200,
            message: t('chuangdu_tips'.replace('s%', 2).replace('e%', 200)),
            trigger: 'blur',
        },
    ],
}
const url =
    process.env.NODE_ENV == 'production'
        ? 'https://ems-api.ssnj.com'
        : 'https://ems-api-beta.ssnj.com'
const fileList = ref([])
const dialogImageUrl = ref('')
const dialogVisible = ref(false)
const hideUpload = ref(false)
const handlePreview = (file) => {
    dialogImageUrl.value = file.url
    dialogVisible.value = true
}

const handleRemove = (file, flist) => {
    fileList.value = flist
    hideUpload.value = flist.length >= 9
}
const beforeUpload = (file) => {
    console.log('[ file.type ] >', file.type)
    if (
        file.type !== 'image/jpeg' &&
        file.type !== 'image/png' &&
        file.type !== 'image/jpg'
    ) {
        ElMessage.error(t('qingshangchuantupian'))
        return false
    } else {
        let isJpgOrPng = ''
        const isLt1M = file.size / 1024 / 1024 <= 1
        if (!isLt1M) {
            // message.error('图片大小不能超过1M')
        }
        return isJpgOrPng && isLt1M
    }
}
const updateFileList = (newFileList, a) => {
    hideUpload.value = a.length >= 9
    fileList.value = a
}
const getUploadData = () => {
    return {
        scene: 'workOrder',
        // 其他需要传递的数据
    }
}
const onClose = () => {
    detailVisible.value = false
    nextTick(() => {
        console.log('[ formRef.value ] >', formRef.value)
        formRef.value.resetFields()
        // formState.cause = ''
        // formState.solution = ''
        fileList.value = []
    })
}

// ⬆️ 工单详情
const stations1 = ref()
const activeSystem = computed(() => localStorage.getItem('activeSystem'))
const getStations = async () => {
    const page = {
        orgId: orgId.value,
        stationType:
            activeSystem.value == 'car'
                ? 'vehicle_battery'
                : 'energy_storage_cabinet',
    }
    const res = await api.getOrgAndSubOrgStationNameList(page)
    stations.value = res.data.data.map((item) => ({
        label: item.stationName,
        value: item.stationNo,
    }))
    stations1.value = res.data.data
}
onBeforeMount(async () => {
    await getUserNames()
    await getStations()
})
watch(
    getAlarmDataFlag,
    async (val) => {
        console.log('[ val ] >', val)
        if (val) {
            await getData()
        }
    },
    { immediate: true, deep: true }
)
const { themeChangeComplete } = useTheme()
const isDark = computed(() => {
    return store.state.theme.isDark
})
watch([isDark, themeChangeComplete], ([newIsDark, isComplete]) => {
    // Only update chart when theme change is complete
    if (isComplete) {
        console.log(isComplete)
        formatData(
            chartData.value.chart1Data,
            'echarts-1',
            props.type == 'alarm'
                ? t('alarm_yichangzhuangtai')
                : t('gongdanzhuangtai')
        )
        formatData(
            chartData.value.chart2Data,
            'echarts-2',
            t('alarm_chulishichang')
        )
        formatData(
            chartData.value.chart3Data,
            'echarts-3',
            t('alarm_chulirenyuan')
        )
    }
})

onMounted(async () => {
    // await getData()
})
</script>

<style lang="less" scoped>
.charts-box {
    padding: 32px 30px;
    // padding-right: 80px;
    border-radius: 8px;
    background: var(--car-pie-border);
    border: 1px solid #f5f5f5;
}

@media screen and (max-width: 1560px) {
    .charts-box {
        padding-right: 32px;
    }
}

.ecahrts-dom {
    width: 200px;
    height: 200px;
    margin-right: 32px;

    // width: 33.33%;
    &:last-child {
        margin-right: 0;
    }
}

:deep(.ant-table-row) {
    cursor: pointer;
}

.list + .list {
    margin-top: 8px;
}

.item + .item {
    margin-top: 8px;
}

:deep(.el-upload--picture-card) {
    --el-upload-picture-card-size: 100px;
}

:deep(.el-upload-list--picture-card) {
    --el-upload-list-picture-card-size: 100px;
}

.hide /deep/ .el-upload--picture-card {
    display: none;
}

:deep(.el-form-item__label) {
    padding-right: 0;
}

:deep(.el-form-item) {
    margin-bottom: 16px;
}

:deep(.ant-calendar-picker .ant-calendar-picker-clear) {
    display: block;
}
.drawer-container-bg {
    background-color: var(--header-bg);
}
</style>
