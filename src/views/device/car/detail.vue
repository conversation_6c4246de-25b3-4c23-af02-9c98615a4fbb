<template>
    <a-spin :spinning="loading">
        <div class="device_detail">
            <div class="flex mb-4 justify-between">
                <!-- <div class="text-base leading-6 go-box inline-block cursor-pointer" @click="goRouter">
                    <span class="bt-box-go rounded"><i class="iconfont icon-jichu-you"></i></span><span class="ml-2">{{
                        stationInfo.stationName || '-'
                        }}</span>
                </div> -->
                <div class="text-base leading-8 go-box flex items-center">
                    <span
                        class="cursor-pointer mr-1 a text-primary-text dark:text-80-dark"
                        @click="goRouter"
                        >{{ selectSupplierInfo.name || '-' }}</span
                    >

                    <iconSvg
                        name="rightIcon"
                        class="more-icon text-primary-text dark:text-80-dark"
                    />
                    <!-- 站点详情 -->
                    <span class="ml-1 text-primary-text dark:text-80-dark">{{
                        stationInfo.stationName
                    }}</span>
                </div>
                <div
                    class="flex cursor-pointer space-x-2 text-title dark:text-title-dark"
                >
                    <el-button
                        plain
                        round
                        class="btn-hover"
                        @click="editDevice"
                    >
                        <span>{{ $t('Device Management') }}</span>
                        <span class="icon-box ml-0.5">
                            <iconSvg name="device" class="icon-default" />
                        </span>
                    </el-button>
                </div>
            </div>
            <div class="flex detail-info mb-3 p-4 items-center h-ful">
                <div class="flex-1 w-0">
                    <div class="flex gap-x-5">
                        <div
                            class="bg-ff dark:bg-ff-dark rounded overflow-hidden"
                            style="width: 333px; height: 250px"
                        >
                            <img
                                :src="stationInfo.stationPic"
                                class="w-full h-full"
                                alt=""
                                srcset=""
                            />
                        </div>
                        <div class="flex-1 pr-10 w-0 pt-4">
                            <div class="flex justify-between items-center mb-3">
                                <div class="flex flex-1 items-center gap-x-5">
                                    <div
                                        class="overflow text-2xl font-medium h-6 leading-6 flex-1 w-0 text-title dark:text-title-dark"
                                        v-if="stationInfo.stationName"
                                    >
                                        {{ stationInfo.stationName || '' }}
                                    </div>
                                    <div
                                        class="overflow text-2xl font-medium bg-ff dark:bg-ff-dark h-8 w-40"
                                        v-else
                                    ></div>
                                    <div
                                        class="h-6 w-6 cursor-pointer text-title dark:text-title-dark"
                                        @click="handleEditInfo"
                                    >
                                        <iconSvg name="edit" class="w-6 h-6" />
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center mb-9">
                                <div
                                    class="text-xs text-secondar-text dark:text-60-dark leading-6"
                                >
                                    {{ $t('No') }}：{{
                                        stationInfo.stationNo || '-'
                                    }}
                                </div>
                                <div
                                    class="flex items-center leading-6 gap-x-2 ml-2"
                                >
                                    <div
                                        class="flex items-center gap-x-1 px-2 rounded-sm"
                                        style="
                                            color: #1677ff;
                                            background: rgba(22, 119, 255, 0.1);
                                        "
                                    >
                                        <div class="text-xs leading-5.5">
                                            {{ $t('Vehicles') }}:{{
                                                stationInfo.deviceQuantity
                                            }}
                                            {{ $t('tai') }}
                                        </div>
                                    </div>

                                    <div
                                        v-if="stationInfo.offlineDeviceQuantity"
                                        class="flex items-center gap-x-1"
                                        :style="{
                                            color: '#FD750B',
                                        }"
                                    >
                                        <i
                                            class="w-6 h-6 text-2xl leading-6"
                                            :class="[
                                                'iconfont',
                                                getState(0).icon,
                                            ]"
                                        ></i>
                                        <span>{{ $t('status_lixian') }}</span
                                        >{{ stationInfo.offlineDeviceQuantity }}
                                    </div>
                                </div>
                            </div>

                            <div
                                class="inline-flex justify-between items-center gap-x-10 mb-3"
                            >
                                <div class="">
                                    <div
                                        class="text-secondar-text dark:text-60-dark leading-4 mb-2"
                                    >
                                        {{
                                            $t('station_zhuangjirongliang')
                                        }}(Ah)
                                    </div>
                                    <div
                                        class="text-base font-medium leading-4 text-title dark:text-title-dark"
                                    >
                                        {{
                                            stationInfo.installedCapacity || '-'
                                        }}
                                    </div>
                                </div>
                                <!-- <a-divider type="vertical" class="h-10 w-0.5" /> -->
                                <div class="w-0.5 h-10 bg-border"></div>
                                <div class="text-center">
                                    <div class="inline-block text-left">
                                        <div
                                            class="text-secondar-text dark:text-60-dark leading-4 mb-2"
                                        >
                                            {{
                                                $t('station_zhuangjigonglv')
                                            }}(kW)
                                        </div>
                                        <div
                                            class="text-base font-medium leading-4 text-title dark:text-title-dark"
                                        >
                                            {{
                                                stationInfo.installedPower ||
                                                '-'
                                            }}
                                        </div>
                                    </div>
                                </div>
                                <div class="w-0.5 h-10 bg-border"></div>
                                <div class="text-center">
                                    <div class="inline-block text-left">
                                        <div
                                            class="text-secondar-text dark:text-60-dark leading-4 mb-2"
                                        >
                                            {{ $t('station_touyunriqi') }}
                                        </div>
                                        <div
                                            class="text-base font-medium leading-4 text-title dark:text-title-dark"
                                        >
                                            {{
                                                stationInfo.createTime
                                                    ? dayjs(
                                                          stationInfo.createTime
                                                      ).format('YYYY/MM/DD')
                                                    : '-'
                                            }}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3.5">
                                <div
                                    class="px-2 py-2 bg-f5f7f7 dark:bg-ffffff-dark rounded text-secondar-text dark:text-60-dark opacity-80 overflow"
                                    v-if="getCompanyInfo?.orgType == 'supplier'"
                                >
                                    <div
                                        class="overflow float-left"
                                        style="
                                            max-width: 50%;
                                            padding-right: 2%;
                                            line-height: 14px;
                                        "
                                        :title="stationInfo.supplierName"
                                    >
                                        {{ $t('station_fuwushang') }}：{{
                                            stationInfo.supplierName
                                        }}
                                    </div>
                                    <div
                                        class="overflow float-left"
                                        style="
                                            max-width: 50%;
                                            padding-left: 2%;
                                            border-left: 1px solid #d9d9d9;
                                            line-height: 14px;
                                        "
                                        :title="stationInfo.customerName"
                                    >
                                        {{ $t('station_kehu') }}：{{
                                            stationInfo.customerName
                                        }}
                                    </div>
                                </div>
                            </div>

                            <div
                                class="flex text-secondar-text dark:text-60-dark opacity-80 leading-5.5"
                            >
                                <iconSvg
                                    name="ip"
                                    class="w-4 h-4"
                                    style="margin-top: 3px"
                                />
                                <div
                                    class="ml-1 w-0 flex-1 mutiLineOverflow"
                                    :title="stationInfo.address"
                                >
                                    {{ $t('station_zhandiandizhi') }}：{{
                                        stationInfo.address
                                    }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <editCarInfo
                        v-model:visible="infoVisible"
                        @onClose="onClose"
                        @update="onUpdate"
                        :info="deviceInfo"
                    />
                </div>
                <a-divider type="vertical" class="m-0 h-52" />
                <div
                    class="pie flex items-center justify-between px-7"
                    style="width: 530px"
                >
                    <div class="flex-1">
                        <statusPieChart v-model:data="statusData" />
                    </div>
                    <div class="flex-1 text-center">
                        <socPieChart v-model:data="SOCData" />
                    </div>
                </div>
            </div>
            <div class="flex gap-x-3 w-full">
                <!-- 充放电 -->
                <div
                    class="w-1/2 px-4 pt-3 pb-4 bg-ff dark:bg-car-pie-border rounded cursor-pointer tabs-box"
                    :class="activeKey === '1' ? 'active' : ''"
                    @click="changeTab('1')"
                >
                    <div class="">
                        <div
                            class="h-12 px-5 py-2 rounded text-sm font-medium leading-12 mb-2 flex items-end text-title dark:text-title-dark"
                            style="background: rgba(149, 158, 195, 0.1)"
                        >
                            <div class="leading-6 text-base">
                                {{ $t('car_zuorihuoyuecheliang') }}
                            </div>
                            <div class="text-3.5xl leading-8 font-bold ml-3">
                                {{ pieceOthenData?.yesterdayActiveCount }}
                            </div>
                            <div class="text-xs leading-5 ml-0.5">
                                {{ $t('tai') }}
                            </div>
                        </div>
                        <div
                            class="flex justify-between items-center px-5 mb-2.5"
                        >
                            <div class="flex-1 text-left leading-6 h-6">
                                <span
                                    class="text-secondar-text dark:text-60-dark"
                                >
                                    {{
                                        pieceOthenData?.beforeYesterdayActiveCount ==
                                        0
                                            ? $t('Previous day: No data')
                                            : $t('station_jiaoqianyiri') + '：'
                                    }}
                                </span>
                                <percentage
                                    :num="
                                        pieceOthenData.comparedBeforeYesterdayPercent
                                    "
                                    class="ml-3"
                                />
                            </div>
                        </div>
                        <a-divider class="m-0" />
                        <div
                            class="flex justify-between items-center px-5 mt-3.5 leading-4"
                        >
                            <div class="flex-1 text-left flex">
                                <div class="flex-1">
                                    <div
                                        class="text-secondar-text dark:text-60-dark mb-2"
                                    >
                                        {{ $t('car_yueleijihuoyueshu') }}
                                    </div>
                                    <div
                                        class="font-medium text-base leading-4 text-title dark:text-title-dark"
                                    >
                                        {{
                                            pieceOthenData.monthlyCumulativeActiveCount
                                        }}
                                        {{ $t('tai') }}
                                    </div>
                                </div>
                                <div class="flex-1">
                                    <div
                                        class="text-secondar-text dark:text-60-dark mb-2"
                                    >
                                        {{ $t('car_zongjihuoyueshu') }}
                                    </div>
                                    <div
                                        class="font-medium text-base leading-4 text-title dark:text-title-dark"
                                    >
                                        {{
                                            pieceOthenData.yearCumulativeActiveCount
                                        }}
                                        {{ $t('tai') }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div
                    class="w-1/2 px-4 pt-3 pb-4 bg-ff dark:bg-car-pie-border rounded cursor-pointer tabs-box"
                    :class="activeKey === '2' ? 'active' : ''"
                    @click="changeTab('2')"
                >
                    <div class="">
                        <div
                            class="charge-title flex justify-between text-title dark:text-title-dark"
                        >
                            <div
                                class="charge-title-l h-12 pl-3 py-2 leading-12 rounded-l text-sm font-medium flex items-end"
                            >
                                <div class="leading-6 text-base">
                                    {{ $t('station_zuorichongdianliang') }}
                                </div>
                                <div
                                    class="text-3.5xl leading-8 font-bold ml-4.5"
                                >
                                    {{ chargeStatisticsData.yesterdayCharge }}
                                </div>
                                <div class="text-xs leading-5 ml-0.5">
                                    {{ 'Ah' }}
                                </div>
                            </div>
                            <div
                                class="charge-title-r h-12 pr-3 py-2 leading-12 rounded-l text-sm font-medium flex items-end justify-end"
                            >
                                <div class="leading-6 text-base">
                                    {{ $t('station_zuorifangdianliang') }}
                                </div>
                                <div
                                    class="text-3.5xl leading-8 font-bold ml-4.5"
                                >
                                    {{
                                        chargeStatisticsData.yesterdayDischarge
                                    }}
                                </div>
                                <div class="text-xs leading-5 ml-0.5">
                                    {{ 'Ah' }}
                                </div>
                            </div>
                        </div>
                        <div
                            class="flex justify-between items-center px-3"
                            style="line-height: 42px"
                        >
                            <div class="flex-1 text-left">
                                <span
                                    class="text-secondar-text dark:text-60-dark"
                                >
                                    {{
                                        chargeStatisticsData?.beforeYesterdayCharge ==
                                        0
                                            ? $t('Previous day: No data')
                                            : $t('station_jiaoqianyiri') + '：'
                                    }}
                                </span>
                                <percentage
                                    :num="
                                        chargeStatisticsData.comparedChargePercent
                                    "
                                    class="ml-3"
                                />
                            </div>
                            <div class="flex-1 text-right">
                                <span
                                    class="text-secondar-text dark:text-60-dark"
                                >
                                    {{
                                        chargeStatisticsData?.beforeYesterdayDischarge ==
                                        0
                                            ? $t('Previous day: No data')
                                            : $t('station_jiaoqianyiri') + '：'
                                    }}
                                </span>
                                <percentage
                                    :num="
                                        chargeStatisticsData.comparedDischargePercent
                                    "
                                    class="ml-3"
                                />
                            </div>
                        </div>
                        <a-divider class="m-0" />
                        <div
                            class="flex justify-between items-center px-3 mt-3.5 leading-4"
                        >
                            <div class="flex-1 text-left flex">
                                <div>
                                    <div
                                        class="text-secondar-text dark:text-60-dark mb-2"
                                    >
                                        {{ $t('station_yueleiji') }}
                                    </div>
                                    <div
                                        class="font-medium text-base leading-4 text-title dark:text-title-dark"
                                    >
                                        {{
                                            chargeStatisticsData.currentMonthCharge
                                        }}
                                        Ah
                                    </div>
                                </div>
                                <div class="ml-2">
                                    <div
                                        class="text-secondar-text dark:text-60-dark mb-2"
                                    >
                                        {{ $t('station_zongji') }}
                                    </div>
                                    <div
                                        class="font-medium text-base leading-4 text-title dark:text-title-dark"
                                    >
                                        {{ chargeStatisticsData.totalCharge }}
                                        Ah
                                    </div>
                                </div>
                            </div>
                            <div class="flex-1 text-right flex justify-end">
                                <div>
                                    <div
                                        class="text-secondar-text dark:text-60-dark mb-2"
                                    >
                                        {{ $t('station_yueleiji') }}
                                    </div>
                                    <div
                                        class="font-medium text-base leading-4 text-title dark:text-title-dark"
                                    >
                                        {{
                                            chargeStatisticsData.currentMonthDischarge
                                        }}
                                        Ah
                                    </div>
                                </div>
                                <div class="ml-2">
                                    <div
                                        class="text-secondar-text dark:text-60-dark mb-2"
                                    >
                                        {{ $t('station_zongji') }}
                                    </div>
                                    <div
                                        class="font-medium text-base leading-4 text-title dark:text-title-dark"
                                    >
                                        {{
                                            chargeStatisticsData.totalDischarge
                                        }}
                                        Ah
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="topTabs">
                <a-tabs class="w-full tabs pt-1" :activeKey="activeKey">
                    <a-tab-pane key="1" tab="a">
                        <div
                            class="p-4 rounded-lg bg-ff dark:bg-car-pie-border mb-3"
                        >
                            <div class="flex mb-2.5 items-center">
                                <div class="flex-1 w-0">
                                    <el-tabs
                                        v-model="activeDevice"
                                        @tab-change="handleDeviceTabChange"
                                    >
                                        <template #default>
                                            <el-tab-pane
                                                :label="item.deviceName"
                                                :name="item.deviceSn"
                                                v-for="item in deviceList"
                                                :key="item.deviceSn"
                                            >
                                            </el-tab-pane>
                                        </template>
                                    </el-tabs>
                                </div>
                                <div class="flex gap-x-3">
                                    <el-popconfirm
                                        :title="
                                            bmsInfoData.lockSta == 1
                                                ? $t('Are you sure unlock?')
                                                : $t('Are you sure lock?')
                                        "
                                        @confirm="OffDevice"
                                        :confirm-button-text="$t('common_shi')"
                                        :cancel-button-text="$t('common_fou')"
                                        width="240"
                                    >
                                        <template #reference>
                                            <el-button
                                                plain
                                                round
                                                class="btn-hover"
                                                :disabled="
                                                    bmsInfoData.onlineStatus ===
                                                        0 ||
                                                    bmsInfoData.lockSta === null
                                                "
                                            >
                                                <span>{{
                                                    bmsInfoData.lockSta == 1
                                                        ? $t('jiesuo')
                                                        : $t('suoche')
                                                }}</span>
                                                <span class="icon-box ml-1">
                                                    <iconSvg
                                                        :name="
                                                            bmsInfoData.lockSta ==
                                                            1
                                                                ? 'unlock'
                                                                : 'lock'
                                                        "
                                                        class="icon-default"
                                                    />
                                                </span>
                                            </el-button>
                                        </template>
                                    </el-popconfirm>

                                    <toggleView
                                        @change="onChangeDeviceDataView"
                                        v-model:type="deviceDataViewType"
                                        class="align-middle"
                                    />
                                    <el-button
                                        plain
                                        round
                                        class="btn-hover"
                                        @click="handleRefresh"
                                    >
                                        <span>{{ $t('Refresh') }}</span>
                                        <span class="icon-box ml-1">
                                            <iconSvg
                                                name="refresh"
                                                class="icon-default"
                                            />
                                        </span>
                                    </el-button>
                                </div>
                            </div>
                            <div
                                class="device-box"
                                v-if="deviceDataViewType == 'chart'"
                            >
                                <div class="device-img">
                                    <car-img
                                        :vehicleType="
                                            activeDeviceInfo?.vehicleType
                                        "
                                    />
                                </div>
                                <el-divider
                                    direction="vertical"
                                    style="height: 325px"
                                />
                                <div class="device-info">
                                    <div
                                        class="content-desc"
                                        v-if="!showBmsBox"
                                    >
                                        <bmsBox
                                            :data="{
                                                ...bmsInfoData,
                                                createTime:
                                                    stationInfo.createTime,
                                            }"
                                            :deviceData="activeDeviceInfo"
                                            @lookBattery="lookBattery(true)"
                                        />
                                    </div>

                                    <div class="cell h-full" v-if="showBmsBox">
                                        <cellBox
                                            :data="cellData"
                                            :parallelCount="
                                                activeDeviceInfo.parallelCount
                                            "
                                            @lookBms="lookBattery(false)"
                                        />
                                    </div>
                                </div>
                            </div>
                            <div
                                class="w-full"
                                style="height: 403px"
                                v-if="deviceDataViewType == 'table'"
                            >
                                <div
                                    class="w-full flex justify-end items-center gap-x-3"
                                >
                                    <div class="flex items-center">
                                        <el-select
                                            v-model="deviceType"
                                            :placeholder="$t('Device Type')"
                                            style="width: 120px"
                                            class=""
                                            @change="onTypeChange"
                                        >
                                            <el-option
                                                v-for="item in deviceTypes"
                                                :key="item.value"
                                                :label="item.label"
                                                :value="item.value"
                                            />
                                        </el-select>
                                    </div>

                                    <div class="flex items-center">
                                        <el-select
                                            v-model="showField"
                                            :placeholder="
                                                $t('station_zhanshiziduan')
                                            "
                                            style="width: 150px"
                                            class=""
                                            multiple
                                            :multiple-limit="30"
                                            collapse-tags
                                            collapse-tags-tooltip
                                            @change="onShowFieldChange"
                                        >
                                            <el-option
                                                v-for="item in showFields"
                                                :key="item.value"
                                                :label="item.label"
                                                :value="item.value"
                                            />
                                        </el-select>
                                    </div>

                                    <div class="flex items-center">
                                        <date-search
                                            :info="{
                                                periodOptions: [],
                                                datePickerType: 'day',
                                                minDate: cascaderStartDate,
                                                dayRangeLen: 2,
                                            }"
                                            v-model:dateSelect="rangeDate"
                                            @onChange="onDateChange"
                                        />
                                    </div>
                                </div>
                                <div
                                    class="table-tabs mt-3"
                                    style="height: 300px"
                                >
                                    <device-data
                                        v-model:tableData="otherTableData"
                                        :tableColumn="otherTableColumn"
                                        :loading="tableLoading"
                                        :showMore="showMore"
                                        @loadMore="loadMore"
                                        :loadMoreLoading="loadMoreLoading"
                                    />
                                    <!-- 这个分页由于时间关系和数据在外面，所以先写外面了 -->
                                    <!-- <div class="text-center mt-4">
                                        <el-button
                                            plain
                                            round
                                            v-if="showMore"
                                            :loading="loadMoreLoading"
                                            @click="loadMore"
                                            >{{
                                                $t('jiazaigengduo')
                                            }}</el-button
                                        >
                                        <div
                                            v-if="
                                                !showMore &&
                                                otherTableData.length
                                            "
                                        >
                                            {{ $t('No more data') }}
                                        </div>
                                    </div> -->
                                </div>
                            </div>
                            <div
                                class="mb-3 flex gap-x-3 text-title dark:text-title-dark"
                            >
                                <div
                                    class="flex-1 my-m-r-3 relative my-rounded-lg overflow-hidden pb-4 box-border"
                                >
                                    <div
                                        class="flex justify-between items-center p-4"
                                    >
                                        <div class="left-4 title-family">
                                            {{
                                                $t(
                                                    'station_24xiaoshidianliangbianhua'
                                                )
                                            }}
                                        </div>
                                        <div class="mr-0">
                                            <date-search
                                                :info="{
                                                    periodOptions: [],
                                                    datePickerType: 'hour',
                                                    minDate: moment(
                                                        stationInfo.createTime
                                                    ).format('YYYY-MM-DD'),
                                                }"
                                                @onChange="pickChange"
                                                v-model:dateSelect="pickerTime"
                                            />
                                        </div>
                                    </div>
                                    <div class="text-center mb-1.5">
                                        <div
                                            class="top-4 title-family flex justify-center items-center leading-6"
                                        >
                                            <span class="mr-4 flex items-center"
                                                ><span class="raduis-box"></span
                                                ><span class="my-m-l-1">{{
                                                    $t('soc')
                                                }}</span></span
                                            >
                                            <span class="mr-4 flex items-center"
                                                ><img
                                                    src="@/assets/device/lightning-3.png"
                                                /><span class="my-m-l-1">{{
                                                    $t('status_chongdian')
                                                }}</span></span
                                            >
                                            <span class="flex items-center"
                                                ><img
                                                    src="@/assets/device/lightning-4.png"
                                                /><span class="my-m-l-1">{{
                                                    $t('status_fangdian')
                                                }}</span></span
                                            >
                                        </div>
                                    </div>
                                    <div class="demo-echart">
                                        <div
                                            id="demo"
                                            style="height: 303px; width: 100%"
                                        ></div>
                                    </div>
                                </div>
                                <div
                                    class="flex-1 relative my-rounded-lg overflow-hidden box-border"
                                >
                                    <div
                                        class="flex justify-between items-center p-4"
                                    >
                                        <div class="top-4 left-4 title-family">
                                            {{
                                                $t(
                                                    'station_chongfangdiantongji'
                                                )
                                            }}
                                        </div>
                                        <div
                                            class="top-3 right-5 flex items-center"
                                        >
                                            <date-search
                                                :info="{
                                                    periodOptions: [],
                                                    datePickerType: 'day',
                                                    dayRangeLen: 7,
                                                    minDate: moment(
                                                        stationInfo.createTime
                                                    ).format('YYYY-MM-DD'),
                                                }"
                                                @onChange="
                                                    deviceChargeDateChange
                                                "
                                                v-model:dateSelect="
                                                    efficiencySelect
                                                "
                                            />
                                        </div>
                                    </div>
                                    <div
                                        id="lineCharts"
                                        style="height: 333px; width: 100%"
                                    ></div>
                                </div>
                            </div>

                            <div
                                class="flex justify-between electricity bt-box text-title dark:text-title-dark"
                                id="analysis"
                            >
                                <div class="flex-1 rounded overflow-hidden">
                                    <div class="p-2.5 text-sm">
                                        {{
                                            $t('station_cuneidianxinyachafenxi')
                                        }}
                                    </div>
                                    <div class="w-full flex flex-col bg-bt">
                                        <div class="flex justify-between">
                                            <div class="my-tab">
                                                <div
                                                    :class="{
                                                        'tab-item': true,
                                                        'title-family': true,
                                                        'active-tab-item':
                                                            tabIndexOne == i,
                                                    }"
                                                    @click="voltageClick(i)"
                                                    v-for="(item, i) in tabList"
                                                    :key="i"
                                                >
                                                    {{ item }}
                                                </div>
                                            </div>
                                            <div class="tab-select">
                                                <a-select
                                                    :options="[
                                                        {
                                                            label: $t(
                                                                'common_jintian'
                                                            ),
                                                            value: '0',
                                                        },
                                                        {
                                                            label: $t(
                                                                'Last Week'
                                                            ),
                                                            value: '1',
                                                        },
                                                        {
                                                            label: $t(
                                                                'Last 30 Days'
                                                            ),
                                                            value: '2',
                                                        },
                                                    ]"
                                                    :placeholder="
                                                        $t(
                                                            'placeholder_qingxuanze'
                                                        )
                                                    "
                                                    class="w-30"
                                                    v-model:value="voltageTime"
                                                    @change="voltageChange"
                                                />
                                            </div>
                                        </div>
                                        <a-spin :spinning="voltageLoading">
                                            <div
                                                id="voltage"
                                                class="voltage my-rounded-lg overflow-hidden"
                                            ></div>
                                        </a-spin>
                                    </div>
                                </div>
                                <div class="flex-1 rounded overflow-hidden">
                                    <div class="p-2.5 text-sm">
                                        {{
                                            $t(
                                                'station_cuneidainxinwenchafenxi'
                                            )
                                        }}
                                    </div>
                                    <div class="w-full flex flex-col bg-bt">
                                        <div class="flex justify-between">
                                            <div class="my-tab">
                                                <div
                                                    :class="{
                                                        'tab-item': true,
                                                        'title-family': true,
                                                        'active-tab-item':
                                                            tabIndexTwo == i,
                                                    }"
                                                    v-for="(item, i) in tabList"
                                                    :key="i"
                                                    @click="electricaClick(i)"
                                                >
                                                    {{ item }}
                                                </div>
                                            </div>
                                            <div class="tab-select">
                                                <a-select
                                                    :options="[
                                                        {
                                                            label: $t(
                                                                'common_jintian'
                                                            ),
                                                            value: '0',
                                                        },
                                                        {
                                                            label: $t(
                                                                'Last Week'
                                                            ),
                                                            value: '1',
                                                        },
                                                        {
                                                            label: $t(
                                                                'Last 30 Days'
                                                            ),
                                                            value: '2',
                                                        },
                                                    ]"
                                                    :placeholder="
                                                        $t(
                                                            'placeholder_qingxuanze'
                                                        )
                                                    "
                                                    class="w-30"
                                                    v-model:value="
                                                        electricalTime
                                                    "
                                                    @change="electricalChange"
                                                />
                                            </div>
                                        </div>
                                        <a-spin :spinning="electricLoading">
                                            <div
                                                id="electric"
                                                class="voltage my-rounded-lg overflow-hidden"
                                            ></div>
                                        </a-spin>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <alarm-overview
                            station-type="vehicle_battery"
                            v-model:getAlarmDataFlag="getAlarmDataFlag"
                        />
                    </a-tab-pane>
                    <a-tab-pane key="2" tab="b">
                        <div
                            class="p-4 bg-ff dark:bg-car-pie-border rounded-lg"
                        >
                            <div
                                class="flex justify-between items-center mb-6 text-title dark:text-title-dark"
                            >
                                <div>
                                    {{ $t('station_chongfangdiantongji') }}
                                </div>
                                <div class="flex justify-between gap-x-3">
                                    <date-search
                                        :info="{
                                            periodOptions: ['day', 'month'],
                                            datePickerType: 'minute',
                                            dayRangeLen: 7,
                                            minDate: moment(
                                                stationInfo.createTime
                                            ).format('YYYY-MM-DD'),
                                        }"
                                        @onChange="chargeDateSearchChange"
                                        v-model:dateSelect="chargeDateSelect"
                                    />
                                    <!-- <el-button
                                        plain
                                        round
                                        @click="lookDetail('profit')"
                                        class="border-1 relative btn-hover"
                                    >
                                        <span>查看明细</span>
                                        <span class="icon-box ml-0.5">
                                            <iconSvg
                                                name="search"
                                                class="icon-default"
                                            />
                                        </span>
                                    </el-button> -->
                                    <toggleView
                                        @change="onChangeView"
                                        v-model:type="chargeViewType"
                                    />
                                    <el-popconfirm
                                        :title="
                                            $t('export_tips01')
                                                .replace(
                                                    's%',
                                                    chargeDateSelect?.startDate ||
                                                        chargeDateSelect?.startMonth
                                                )
                                                .replace(
                                                    's%',
                                                    chargeDateSelect?.endDate ||
                                                        chargeDateSelect?.endMonth
                                                )
                                        "
                                        @confirm="confirmExportProfit"
                                        :confirm-button-text="$t('common_shi')"
                                        :cancel-button-text="$t('common_fou')"
                                        width="240"
                                    >
                                        <template #reference>
                                            <export-button
                                                style="margin-left: 0"
                                            />
                                        </template>
                                    </el-popconfirm>
                                </div>
                            </div>
                            <!-- 393px -->
                            <div
                                class="flex gap-x-5"
                                v-if="chargeViewType == 'chart'"
                            >
                                <div class="flex-1 w-0">
                                    <div
                                        class="w-full"
                                        style="height: 560px"
                                        id="incomeEcharts"
                                    ></div>
                                </div>
                                <div class="rank">
                                    <div
                                        class="text-secondar-text dark:text-60-dark"
                                    >
                                        {{ $t('Charge/Discharge Ranking') }}
                                    </div>
                                    <div
                                        class="rank-header text-secondar-text dark:text-60-dark flex justify-between items-center text-center mt-4"
                                    >
                                        <div class="rank-num h-5.5">
                                            {{ $t('Rank') }}
                                        </div>
                                        <div class="rank-name">
                                            {{ $t('Vehicles') }}
                                        </div>
                                        <div class="flex flex-1 items-center">
                                            <div>
                                                {{
                                                    chargeType === 'charge'
                                                        ? t('Charge')
                                                        : t('Discharge')
                                                }}({{ profitUnit }})
                                            </div>
                                            <div
                                                class="w-5 h-5 flex items-center justify-center cursor-pointer select-none text-center ml-3"
                                                @click="toggleRank"
                                            >
                                                <iconSvg
                                                    name="toggle"
                                                    :className="'toggle'"
                                                />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="">
                                        <template
                                            v-for="(item, index) in rankList"
                                            :key="index"
                                        >
                                            <div
                                                class="pr-1.5 flex items-center mt-6"
                                                v-if="index < 5"
                                            >
                                                <div class="h-5.5 rank-num">
                                                    {{ index + 1 }}
                                                </div>
                                                <div
                                                    class="overflow rank-name"
                                                    :title="item.key"
                                                >
                                                    {{ item.key }}
                                                </div>
                                                <div
                                                    class="flex-1 flex justify-end items-center w-0 rank-process"
                                                >
                                                    <div
                                                        class="text-base leading-5.5 w-18 quantity"
                                                    >
                                                        {{ item.quantity }}
                                                    </div>
                                                    <div class="w-25">
                                                        <BarProgress
                                                            :fullWidth="100"
                                                            :ratio="item.ratio"
                                                        />
                                                    </div>
                                                </div>
                                            </div>
                                        </template>
                                        <template v-if="!rankList?.length">
                                            <empty-data
                                                :description="$t('zanwushuju')"
                                                class="mx-auto mt-10 text-secondar-text dark:text-60-darl"
                                            >
                                                <slot name="empty"></slot>
                                            </empty-data>
                                        </template>
                                    </div>
                                </div>
                            </div>
                            <div
                                v-if="chargeViewType == 'table'"
                                style="height: 560px"
                            >
                                <ChargeData
                                    class="h-full"
                                    v-model:tableData="chargeTableData"
                                />
                            </div>
                        </div>
                        <div class="flex items-center gap-x-3 mt-3">
                            <div
                                class="flex-1 bg-ff dark:bg-car-pie-border rounded-lg p-4"
                            >
                                <div
                                    class="w-full flex justify-between items-center text-title dark:text-title-dark"
                                >
                                    <div>
                                        {{ $t('Service Life Distribution') }}
                                    </div>
                                    <div></div>
                                </div>
                                <div class="w-full" style="height: 330px">
                                    <distributionChart
                                        :chartData="[1]"
                                        type="month"
                                    />
                                </div>
                            </div>
                            <div
                                class="flex-1 bg-ff dark:bg-car-pie-border rounded-lg p-4"
                            >
                                <div
                                    class="w-full flex justify-between items-center text-title dark:text-title-dark"
                                >
                                    <div>
                                        {{ $t('Operating Hours Distribution') }}
                                    </div>
                                    <div></div>
                                </div>
                                <div class="w-full" style="height: 330px">
                                    <distributionChart
                                        :chartData="[2]"
                                        type="hour"
                                    />
                                </div>
                            </div>
                        </div>
                    </a-tab-pane>
                    <a-tab-pane key="3" tab="c">
                        <div
                            class="p-4 bg-ff dark:bg-ff-dark rounded-lg"
                            style="min-height: 360px"
                        ></div>
                    </a-tab-pane>
                </a-tabs>
            </div>
            <edit-device
                v-model:visible="editDeviceVisible"
                :device-list="deviceList"
                @update="onUpdateDevice"
            />
        </div>
    </a-spin>
</template>

<script>
import * as echarts from 'echarts'
import {
    getChargeOption,
    getElectricityData,
    unitConversion,
    alternateUnits,
    updateEcharts,
    filterDate,
    getTimeOption,
    someMax,
    roundNumFun,
} from '../const'

import { getState, getSegmentTypeColor } from '@/common/util.js'
import {
    ref,
    onMounted,
    reactive,
    nextTick,
    computed,
    onBeforeUnmount,
    watch,
} from 'vue'

import { useRoute, useRouter } from 'vue-router'
import apiService from '@/apiService/device'
import carApi from '@/apiService/car'
import apiVpp from '@/apiService/vpp'
import dayjs from 'dayjs'
import _cloneDeep from 'lodash/cloneDeep'
import _round from 'lodash/round'
import bmsBox from './components/bmsBox.vue'
import cellBox from './components/cellBox.vue'

import Percentage from '../components/percentage.vue'
import { useStore } from 'vuex'
import AlarmOverview from '../alarmOverview.vue'
import editCarInfo from './components/editCarInfo.vue'
import moment from 'moment'
import * as XLSX from 'xlsx' //引入 xlsx 库，将数据转换为 Excel 并下载
import DateSearch from '@/components/dateSearch.vue'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import carImg from './components/carImg.vue'
import statusPieChart from './components/statusPieChart.vue'
import socPieChart from './components/socPieChart.vue'
import BarProgress from '../components/barProgress.vue'
import distributionChart from './components/distributionChart.vue'
import EditDevice from './components/editDevice.vue'
import { message } from 'ant-design-vue'
import { ElLoading } from 'element-plus'
import toggleView from '@/components/toggle.vue'
import ChargeData from '@/views/role/components/carChargeData.vue'
import deviceData from '@/views/role/components/deviceData.vue'
import { useI18n } from 'vue-i18n'
import useTheme from '@/common/useTheme'
export default {
    name: 'deviceDetail',
    //
    components: {
        bmsBox,
        cellBox,
        Percentage,
        // WorkOrder,
        AlarmOverview,
        editCarInfo,
        DateSearch,
        carImg,
        statusPieChart,
        socPieChart,
        BarProgress,
        distributionChart,
        EditDevice,
        toggleView,
        ChargeData,
        deviceData,
    },
    setup() {
        const { t, locale } = useI18n()
        const loading = ref(false)

        const store = useStore()

        const route = useRoute()
        const customerDetail = ref(route.query)
        const router = useRouter()

        const tabList = [t('device_jichafenxi'), t('device_fangchafenxi')]

        const tabIndexOne = ref(0)
        const tabIndexTwo = ref(0)

        const efficiencySelect = ref({
            periodType: 'day',
            startDate: moment().subtract(6, 'days').format('YYYY-MM-DD'),
            endDate: moment().subtract(0, 'days').format('YYYY-MM-DD'),
        })
        const voltageTime = ref('0')

        const electricalTime = ref('0')

        const pickerTime = ref()

        //1表示展示整个机柜数据

        const electricLoading = ref(false)

        const voltageLoading = ref(false)

        const defaultImg =
            sessionStorage.getItem('stationPic') ||
            require('@/assets/device/defaultImg.png')

        //获取头部站点信息
        const stationInfo = reactive({
            alarmQuantity: void 0,
            avgTemperature: void 0,
            createTime: void 0,
            address: void 0,
            installedCapacity: void 0,
            installedPower: void 0,
            soc: void 0,
            soh: void 0,
            stationNo: void 0,
            status: void 0,
            stationName: undefined,
            stationPic: defaultImg,
            id: undefined,
        })

        //获取三个块数据
        const pieceOthenData = reactive({
            beforeYesterdayActiveCount: 0,
            yesterdayActiveCount: 0,
            comparedBeforeYesterdayPercent: 0,
            monthlyCumulativeActiveCount: 0,
            yearCumulativeActiveCount: 0,
        })
        const chargeStatisticsData = reactive({
            comparedCharge: 0,
            comparedDischarge: 0,
            comparedProfit: 0,
            currentMonthCharge: 0,
            currentMonthDischarge: 0,
            currentMonthProfit: 0,
            totalCharge: 0,
            totalDischarge: 0,
            totalProfit: 0,
            yesterdayCharge: 0,
            yesterdayDischarge: 0,
            yesterdayProfit: 0,
        })
        // 统计站点tab2 充放电信息统计
        const getStatisticStationSummary = async () => {
            const { stationNo } = customerDetail.value
            const {
                data: { data, code },
            } = await apiService.getStatisticStationSummary({
                stationNo,
                stationType: 'vehicle_battery',
            })
            if (code === 0) {
                Object.keys(data).forEach((key) => {
                    chargeStatisticsData[key] = data[key] || 0
                })
            } else {
                Object.keys(chargeStatisticsData).forEach((key) => {
                    chargeStatisticsData[key] = 0
                })
            }
        }

        const statisticStationActiveDeviceCountVehicle = async () => {
            //
            let res = await carApi.statisticStationActiveDeviceCountVehicle({
                stationId: customerDetail.value.stationId,
            })
            Object.keys(res.data.data).forEach((key) => {
                pieceOthenData[key] = res.data.data[key] || 0
            })
        }
        //头部左边信息
        const getStationInfo = async () => {
            try {
                const { stationNo } = customerDetail.value
                const {
                    data: { data, code },
                } = await carApi.getStationInfoVehicle({
                    stationId: customerDetail.value.stationId,
                })
                if (code === 0) {
                    Object.keys(data).forEach((key) => {
                        stationInfo[key] = data[key]
                        if (key == 'stationPic') {
                            stationInfo[key] = data[key]
                                ? data[key]
                                : defaultImg
                        }
                    })
                } else {
                    stationInfo.stationPic = defaultImg
                }
            } catch (error) {
                stationInfo.stationPic = defaultImg
            }
        }
        //充放电统计
        //
        const setChargeOptions = () => {
            let options = _cloneDeep(getChargeOption())
            options.xAxis.data = dateList2
            options.yAxis.name = 'Ah'
            options.series[0].data = chargeData2.value
            options.series[1].data = dischargeData2.value
            updateEcharts('incomeEcharts', options)
        }
        const syData = ref()
        const dateList2 = ref()
        const chargeData2 = ref([])
        const dischargeData2 = ref([])
        const getCharge = async (params, id, option) => {
            if (chargeViewType.value == 'chart') {
                try {
                    const {
                        data: { data, code },
                    } = await apiService.getElectricityAndRevenue(params)
                    if (code === 0) {
                        dateList2.value = data.map((item) => {
                            if (params.periodType == 'month') {
                                return dayjs(item.date).format('YYYY-MM')
                            }
                            return dayjs(item.date).format('MM/DD')
                        })
                        chargeData2.value = data.map((item) => {
                            return item.charge
                        })
                        dischargeData2.value = data.map((item) => {
                            return item.discharge
                        })
                        setChargeOptions()
                    } else {
                        updateEcharts(id, option)
                    }
                } catch (error) {
                    updateEcharts(id, option)
                }
            }
            if (chargeViewType.value == 'table') {
                // 获取table数据
                const res1 = await apiVpp.statisticsDailyChargeAndProfitDetail({
                    ...params,
                })
                chargeTableData.value = res1.data.data
            }
        }

        const getDeviceDetail = () => {
            //
        }
        const getVehicleBmsInfo = async () => {
            let res = await carApi.getVehicleBmsInfo({
                stationNo: customerDetail.value.stationNo,
                deviceSn: activeDevice.value,
            })
            Object.keys(res.data.data).forEach((key) => {
                bmsInfoData[key] = res.data.data[key]
            })
        }
        // 获取车辆信息
        const getDeviceDatas = () => {
            getDeviceDetail()
            getVehicleBmsInfo()
        }

        //获取车辆tab
        const getDeviceListVehicle = async () => {
            const { stationId } = customerDetail.value
            const {
                data: { data, code },
            } = await carApi.getDeviceListVehicle({ stationId })
            if (code === 0) {
                deviceList.value = data.map((item) => {
                    return {
                        ...item,
                    }
                })
                activeDevice.value = deviceList.value[0]?.deviceSn
                if (activeDevice.value) {
                    nextTick(() => {
                        getDeviceDatas() // 获取当前设备(车辆)信息
                    })
                }
            }
        }

        //电池柜数据
        const bmsInfoData = reactive({
            runStatus: void 0,
            chargeStatus: void 0,
            soh: void 0,
            soc: void 0,
            totalVoltage: void 0,
            totalElectricity: void 0,
            chargeCapacity: void 0,
            dischargeCapacity: void 0,
            insulationValue: void 0,
            avgTemperature: void 0,
            avgVoltage: void 0,
            maxTemperatureId: void 0,
            maxTemperature: void 0,
            minTemperatureId: void 0,
            minTemperature: void 0,
            maxVoltageId: void 0,
            maxVoltage: void 0,
            minVoltageId: void 0,
            minVoltage: void 0,
            lockSta: undefined,
        })

        const voltageRange = ref([])
        const voltageVariance = ref([])
        const rangeAndVarianceDate = ref([])
        const getBatteryVoltageRangeAndVariance = async (
            params,
            id,
            option
        ) => {
            try {
                voltageLoading.value = true
                const {
                    data: { data, code },
                } = await apiService.getBatteryVoltageRangeAndVariance(params)
                voltageLoading.value = false
                if (code === 0) {
                    rangeAndVarianceDate.value = Object.keys(data)
                    voltageRange.value = Object.values(data).map((item) => {
                        return item?.voltageRange || 0
                    })

                    voltageVariance.value = Object.values(data).map((item) => {
                        return item?.voltageVariance || 0
                    })
                    const options = _cloneDeep(option)
                    options.xAxis.data = rangeAndVarianceDate.value
                    if (tabIndexOne.value == 0) {
                        options.series[0].data = voltageRange.value
                    } else {
                        options.series[0].data = voltageVariance.value
                    }

                    updateEcharts(id, options)
                } else {
                    updateEcharts(id, option)
                }
            } catch (error) {
                voltageLoading.value = false
                updateEcharts(id, option)
            }
        }

        const tempRange = ref([])
        const tempVariance = ref([])
        const tempDateAll = ref([])
        const getBatteryTempRangeAndVariance = async (params, id, option) => {
            try {
                electricLoading.value = true
                const {
                    data: { data, code },
                } = await apiService.getBatteryTempRangeAndVariance(params)
                electricLoading.value = false
                if (code === 0) {
                    tempDateAll.value = Object.keys(data)
                    tempRange.value = Object.values(data).map((item) => {
                        return item?.tempRange || 0
                    })
                    tempVariance.value = Object.values(data).map((item) => {
                        return item?.tempVariance || 0
                    })
                    const options = _cloneDeep(option)
                    options.yAxis.name = t('unit_danwei') + '/°C'
                    options.xAxis.data = tempDateAll.value
                    if (tabIndexTwo.value == 0) {
                        options.series[0].data = tempRange.value
                    } else {
                        options.series[0].data = tempVariance.value
                    }

                    updateEcharts(id, options)
                } else {
                    updateEcharts(id, option)
                }
            } catch (error) {
                electricLoading.value = false
                updateEcharts(id, option)
            }
        }

        const voltageClick = (index) => {
            tabIndexOne.value = index
            const option = _cloneDeep(getElectricityData())
            option.xAxis.data = rangeAndVarianceDate.value || []
            if (index == 0) {
                option.series[0].data = voltageRange.value || []
            } else {
                option.series[0].data = voltageVariance.value || []
            }
            updateEcharts('voltage', option)
        }

        const electricaClick = (index) => {
            tabIndexTwo.value = index
            const option = _cloneDeep(getElectricityData())
            option.xAxis.data = tempDateAll.value || []
            option.yAxis.name = t('unit_danwei') + '/°C'
            if (index == 0) {
                option.series[0].data = tempRange.value || []
            } else {
                option.series[0].data = tempVariance.value || []
            }

            updateEcharts('electric', option)
        }

        const initDom = () => {
            const options = _cloneDeep(getTimeOption())
            options.xAxis[0].data = time24H.value
            options.xAxis[1].data = time24H.value
            options.series[0].data = echartsData24H.value
            options.series[1].data = indexData24H.value
            const demo = document.getElementById('demo')
            demo && echarts.dispose(demo)
            const setOption = echarts.init(demo)
            setOption && setOption.setOption(options)
        }

        // 1
        const voltageChange = async () => {
            const { stationNo } = customerDetail.value
            const [startDate, endDate] = filterDate(voltageTime.value)
            await getBatteryVoltageRangeAndVariance(
                {
                    stationNo,
                    deviceSn: activeDevice.value,
                    startDate,
                    endDate,
                },
                'voltage',
                getElectricityData()
            )
        }

        const electricalChange = async () => {
            const { stationNo } = customerDetail.value
            const [start, end] = filterDate(electricalTime.value)
            await getBatteryTempRangeAndVariance(
                {
                    stationNo,
                    deviceSn: activeDevice.value,
                    startDate: start,
                    endDate: end,
                },
                'electric',
                getElectricityData()
            )
        }

        // 切换24小时电量日期选择框
        const chargeOptions = ref({})
        const time24H = ref([])
        const echartsData24H = ref([])
        const indexData24H = ref([])
        const pickChange = async (params) => {
            const { stationNo } = customerDetail.value
            //
            const {
                data: { data, code },
            } = await carApi.statisticsDeviceSocAndWorkStatusByDay({
                stationNo,
                day: pickerTime.value.startDate,
                deviceSn: activeDevice.value,
            })
            if (code === 0) {
                const colors = [
                    'rgba(0,0,0,0)',
                    'rgba(253, 117, 11, 0.1)',
                    'rgba(30, 204, 153, 0.1)',
                ]
                time24H.value = data.map((item) => {
                    return dayjs(item.time).format('HH:mm')
                })
                time24H.value.push('24:00')
                echartsData24H.value = data.map((item) => item.soc)
                echartsData24H.value.push(0)
                indexData24H.value = data.map((item) => {
                    return {
                        value: 100,
                        itemStyle: { color: colors[item.batteryStatus] },
                    }
                })
                initDom()
            }
        }
        const { themeChangeComplete } = useTheme()
        const isDark = computed(() => {
            return store.state.theme.isDark
        })
        watch([isDark, themeChangeComplete], ([newIsDark, isComplete]) => {
            // Only update chart when theme change is complete
            if (isComplete) {
                initDom()
                setChargeOptions()
                setChargeTotalOptions()
                voltageClick(tabIndexOne.value)
                electricaClick(tabIndexOne.value)
            }
        })
        const setChargeTotalOptions = () => {
            let options = _cloneDeep(getChargeOption())
            options.xAxis.data = dateListTotal.value
            options.yAxis.name = 'Ah'
            options.series[0].data = chargeDataTotal.value
            options.series[1].data = dischargeDataTotal.value
            updateEcharts('lineCharts', options)
        }
        const chargeTableData = ref()
        const chargeDataTotal = ref([])
        const dateListTotal = ref([])
        const dischargeDataTotal = ref([])
        // 切换充放电日期选择
        const deviceChargeDateChange = async () => {
            const { stationNo } = customerDetail.value
            let deviceSn = activeDevice.value
            let params = {
                ...efficiencySelect.value,
                stationType: 'vehicle_battery',
                stationNo,
                deviceSn,
            }
            const res = await apiService.getElectricityAndRevenue({
                ...params,
            })
            if (res.data.code === 0) {
                dateListTotal.value = res.data.data.map((item) => {
                    if (params.periodType == 'month') {
                        return dayjs(item.date).format('YYYY-MM')
                    }
                    return dayjs(item.date).format('MM/DD')
                })
                chargeDataTotal.value = res.data.data.map((item) => {
                    return item.charge
                })
                dischargeDataTotal.value = res.data.data.map((item) => {
                    return item.discharge
                })
                setChargeTotalOptions()
            } else {
                //
            }
        }
        // 获取收告警统计
        // tabs2
        const getStationStaticInfoVehicle = async () => {
            const { stationNo } = customerDetail.value
            let res = await carApi.getStationStaticInfoVehicle({
                stationId: customerDetail.value.stationId,
            })
            deviceInfo.value = res.data.data
        }
        const statusData = ref({
            0: 0,
            1: 0,
            2: 0,
            3: 0,
        })
        const statisticStationDevicesStatusProportionVehicle = async () => {
            //
            const res =
                await carApi.statisticStationDevicesStatusProportionVehicle({
                    stationId: customerDetail.value.stationId,
                })
            // statusData.value == res.data.data
            Object.assign(statusData.value, res.data.data)
        }
        const SOCData = ref({
            1: 0,
            2: 0,
            3: 0,
        })
        // 获取SOC分布
        const statisticStationDevicesSocProportion = async () => {
            const res = await carApi.statisticStationDevicesSocProportion({
                stationId: customerDetail.value.stationId,
            })
            SOCData.value = { ...res.data.data }
            Object.assign(SOCData.value, res.data.data)
        }
        const alarmData = ref({
            totalQuantity: undefined,
            processingQuantity: undefined,
            todayQuantity: undefined,
            sevenDayQuantity: undefined,
        })
        const getStatisticalCard = async () => {
            let res = await apiService.getStatisticalCard({
                supplierId: customerDetail.value.supplierId,
                stationNo: customerDetail.value.stationNo,
                stationType: 'vehicle_battery',
            })
            alarmData.value = res.data.data
        }
        const getMainInfo = async () => {
            await getStationInfo() // 基础信息
            await getStationStaticInfoVehicle() // 基础信息静态
            await statisticStationDevicesStatusProportionVehicle() // 获取状态分布
            await statisticStationDevicesSocProportion() // 获取SOC分布
            await statisticStationActiveDeviceCountVehicle() // 获取tabs1站点下设备活跃数量
            await getStatisticStationSummary() // 统计tabs2站点充放电信息统计
            await getStatisticalCard() // 获取tabs3异常统计
            await getDeviceListVehicle() //获取车辆们  内含车辆基础信息getVehicleBmsInfo()
            await pickChange() // 车辆24小时电量变化
            await deviceChargeDateChange() // 车辆充放电图
            //
        }
        const loadedAnalysisData = ref(false)
        const getAnalysisData = async () => {
            var analysisTopHeight = document
                .getElementById('analysis')
                ?.getBoundingClientRect().top
            // 获取屏幕高度
            var windowHeight = document.documentElement.clientHeight
            if (
                analysisTopHeight - windowHeight < 100 &&
                !loadedAnalysisData.value &&
                activeKey.value === '1'
            ) {
                loadedAnalysisData.value = true
                await voltageChange() // 级差
                await electricalChange() // 温差
                // 异常
                getAlarmDataFlag.value = false
                setTimeout(() => {
                    getAlarmDataFlag.value = true
                }, 100)
            }
        }
        onMounted(async () => {
            localStorage.setItem('activeSystem', 'car')
            loading.value = true
            // 获取站点基本信息静态信息
            await getMainInfo()
            loading.value = false
            // await getStationEmsBasicInfo() // 获取ems基础信息
            loadedAnalysisData.value = false
            window.addEventListener('scroll', async function () {
                getAnalysisData()
            })
        })

        // 页面销毁前
        onBeforeUnmount(() => {
            const demo = document.getElementById('demo')
            demo && echarts.dispose(demo)
            const chartDischargeDom = document.getElementById('chartDischarge')
            chartDischargeDom && echarts.dispose(chartDischargeDom)
        })

        const goRouter = () => {
            if (window.history.state?.back) {
                router.go(-1)
            } else {
                router.replace('/car')
            }
        }

        //
        const activeKey = ref('1')

        // 收益

        // 收益日期切换

        // 充放电日期选择
        const chargeDateSelect = ref()
        const chargeDateSearchChange = async (params) => {
            await getCharge(
                {
                    ...chargeDateSelect.value,
                    stationNo: customerDetail.value.stationNo,
                    supplierId: customerDetail.value.supplierId,
                    stationType: 'vehicle_battery',
                },
                'incomeEcharts',
                getChargeOption()
            )
            getRankData() // 获取排名
        }

        const tabsList = ref(['1'])
        // const

        const changeTab = (key) => {
            activeKey.value = key
            if (tabsList.value.includes(key)) {
                return
            } else {
                tabsList.value.push(key)
                if (key === '2') {
                    nextTick(() => {
                        // 统计和排名统计都在这个方法里
                        chargeDateSearchChange()
                    })
                } else if (key === '3') {
                    getAlarmDataFlag.value = false
                    setTimeout(() => {
                        getAlarmDataFlag.value = true
                    }, 100)
                }
            }
        }
        // 异常
        const getAlarmDataFlag = ref(false)

        const infoVisible = ref(false)
        const deviceInfo = ref({})
        const handleEditInfo = async () => {
            infoVisible.value = true
        }
        const onClose = async () => {
            infoVisible.value = false
            await getStationInfo()
        }
        const onUpdate = async () => {
            await getStationInfo()
            await getStationStaticInfoVehicle()
            // await getChargeData()
        }
        // 关闭弹窗，重置数据

        const getCompanyInfo = computed(
            () => store.getters['user/getUserInfoData']
        )
        // 开启
        const exportExcel = (data, headers, fileName) => {
            // 此处当返回json文件时需要先对data进行JSON.stringify处理，其他类型文件不用做处理
            const blob = new Blob([data], {
                type: headers['content-type'],
            })
            let dom = document.createElement('a')
            let url = window.URL.createObjectURL(blob)
            dom.href = url
            dom.download = decodeURI(fileName)
            dom.style.display = 'none'
            document.body.appendChild(dom)
            dom.click()
            dom.parentNode.removeChild(dom)
            window.URL.revokeObjectURL(url)
        }
        const confirmExportProfit = async () => {
            //
            const res = await apiService.exportStationDailyProfitDetail({
                ...chargeDateSelect.value,
                stationNo: route.query.stationNo,
                stationType: 'vehicle_battery',
            })
            const { data, headers } = res
            const stationName = stationInfo.stationName
            const fileName = stationName
                ? stationName +
                  '-' +
                  chargeDateSelect.value.startDate +
                  t('to') +
                  chargeDateSelect.value.endDate +
                  t('station_chongfangdianshouyi') +
                  '.xlsx'
                : chargeDateSelect.value.startDate +
                  t('to') +
                  chargeDateSelect.value.endDate +
                  t('station_chongfangdianshouyi') +
                  '.xlsx'
            exportExcel(data, headers, fileName)
        }
        const lookDetail = (type) => {
            router.push({
                path: '/rolePage',
                query: {
                    stationNo: route.query.stationNo,
                    stationId: stationInfo.id,
                    stationOrgId: stationInfo.customerId,
                    // stationName: stationInfo.stationName,
                    type: type,
                    activeKey: '3',
                    startDate: moment(stationInfo.createTime).format(
                        'YYYY-MM-DD'
                    ),
                },
            })
        }

        // 车辆新属性。多余旧数据后续删除
        const activeDevice = ref('SN0001')
        const deviceList = ref([])
        const activeDeviceInfo = computed(() => {
            return deviceList.value.find(
                (item) => item.deviceSn === activeDevice.value
            )
        })
        const handleDeviceTabChange = async () => {
            // 获取数据。更新图片 deviceInfo
            if (showBmsBox.value) {
                showBmsBox.value = false
            }
            await getVehicleBmsInfo() // 获取bms数据
            // 获取24小时电量
            await pickChange()
            // 获取充放电
            await deviceChargeDateChange()
            loadedAnalysisData.value = false
            getAnalysisData()
            // deviceDataViewType.value = 'chart'
            if (deviceDataViewType.value == 'table') {
                showMore.value = undefined
                allOtherTableData.value = []
                await onSearchOther()
            }
            // await voltageChange()   // 级差
            // await electricalChange()   // 温差
        }
        const cellData = ref([])
        const showBmsBox = ref(false)
        const lookBattery = async (flag) => {
            //

            //  && !cellData.value.length
            if (flag) {
                const loading = ElLoading.service({
                    lock: true,
                    text: 'Loading',
                    background: 'rgba(0, 0, 0, 0.2)',
                })
                let res = await carApi.getVehiclePackList({
                    deviceSn: activeDevice.value,
                    stationNo: customerDetail.value.stationNo,
                })
                cellData.value = res.data.data
                loading.close()
            }
            showBmsBox.value = flag
        }
        const rankList = ref([])
        const profitUnit = ref('Ah')
        const getRankData = async () => {
            const params = {
                supplierId: customerDetail.value.supplierId,
                ...chargeDateSelect.value,
                resourceType: 'device',
                stationType: 'vehicle_battery',
                chargeType: chargeType.value,
                stationNo: customerDetail.value.stationNo,
            }
            const res = await carApi.statisticsDeviceChargeDischargeRank(params)
            rankList.value = res.data.data

            let max = 0
            rankList.value.forEach((item) => {
                if (+max < +item.quantity) max = +item.quantity
            })
            // 处理数据，计算百分比
            if (max) {
                rankList.value.forEach((item) => {
                    item['ratio'] = +(item.quantity / max).toFixed(2)
                })
            }
            rankList.value.sort((a, b) => {
                return b.quantity - a.quantity
            })

            const arr = rankList.value.map((item) => {
                return item.quantity
            })

            // const isBooan = someMax(arr, 1000)
            // if (isBooan) {
            //     rankList.value?.forEach((item) => {
            //         item.quantity = item?.quantity
            //             ? roundNumFun(item.quantity / 10000, 2)
            //             : 0
            //     })
            // }
        }
        const chargeType = ref('charge')
        const toggleRank = async () => {
            chargeType.value =
                chargeType.value === 'charge' ? 'discharge' : 'charge'
            await getRankData()
        }
        const OffDevice = async () => {
            //

            try {
                let res = await carApi.pushDeviceBatteryLockCommand({
                    deviceSn: activeDevice.value,
                    lockCommand: bmsInfoData.lockSta == '1' ? 'unlock' : 'lock', // lock锁定 。unlock解锁
                })
                if (res.data.data) {
                    message.success(
                        bmsInfoData.lockSta == '1' ? '解锁成功' : '锁车成功'
                    )
                }
            } catch (error) {
                //
            }
        }

        const lookHistoryData = () => {
            //
            router.push({
                path: '/rolePage',
                query: {
                    stationNo: route.query.stationNo,
                    stationId: stationInfo.id,
                    stationOrgId: stationInfo.customerId,
                    // stationName: stationInfo.stationName,
                    type: 'other',
                    activeKey: '3',
                    startDate: moment(stationInfo.createTime).format(
                        'YYYY-MM-DD'
                    ),
                },
            })
        }
        const handleRefresh = async () => {
            //
            await handleDeviceTabChange()
            message.success(t('Successed'))
        }
        const editDeviceVisible = ref(false)
        const editDevice = () => {
            //
            editDeviceVisible.value = true
        }
        const onUpdateDevice = async () => {
            loading.value = true
            await getMainInfo()
            loading.value = false
        }
        const selectSupplierInfo = computed(() => {
            return store.state.device.selectSupplierInfo?.name
                ? store.state.device.selectSupplierInfo
                : JSON.parse(localStorage.getItem('selectSupplierInfo'))
        })
        const chargeViewType = ref('chart')
        const onChangeView = async (e) => {
            await chargeDateSearchChange()
        }

        //
        // 数据3详情
        const pageInfo = ref({
            current: 1,
            size: 100,
        })
        const pageTotal = ref(0)
        const deviceDataViewType = ref('chart')

        const onChangeDeviceDataView = async () => {
            //
            if (deviceDataViewType.value == 'chart') {
                //
            }
            //
            if (deviceDataViewType.value == 'table') {
                await onSearchOther()
            }
        }
        const deviceType = ref('CU')
        const types = ref([
            {
                label: 'bms',
                value: 'CU',
            },
            {
                label: '电芯',
                value: 'DCB',
            },
        ])
        const deviceTypes = computed(() => {
            return types.value
        })
        const showField = ref([])
        const showFields = ref([])
        // 类型切换
        const tableLoading = ref(false)
        const onTypeChange = async () => {
            otherTableData.value = []
            allOtherTableData.value = []
            showMore.value = undefined
            tableLoading.value = true
            if (pageInfo.value.current == 1) {
                await onSearchOther()
            } else {
                pageInfo.value.current = 1
            }
            tableLoading.value = false
        }
        const otherTableData = ref([])
        const allOtherTableData = ref([])
        const filedsObj = ref({})
        const getEmsDataColumn = async (type, flag) => {
            if (flag) return
            const res = await apiService.getEmsDataColumn({
                deviceType: type || deviceType.value,
                productType: 'vehicle_battery',
            })
            filedsObj.value = res.data.data
        }
        const otherTableColumn = ref([])
        const onShowFieldChange = async () => {
            const newArr = allOtherTableData.value.map((item) => {
                let newItem = { ...item }
                item.temperature &&
                    typeof item.temperature == 'object' &&
                    item.temperature.forEach((temp, index) => {
                        newItem[`t${index + 1}`] = temp
                    })
                typeof item.temperature == 'object' &&
                    delete newItem.temperature
                item.voltage &&
                    typeof item.voltage == 'object' &&
                    item.voltage.forEach((vol, index) => {
                        newItem[`v${index + 1}`] = vol
                    })
                typeof item.voltage == 'object' && delete newItem.voltage
                return newItem
            })
            // tables数据根据选中的字段进行筛选
            nextTick(() => {
                otherTableData.value = newArr.map((item) => {
                    return Object.fromEntries(
                        Object.entries(item).filter(([key]) =>
                            showField.value.includes(key)
                        )
                    )
                })
                const arr = Object.entries(filedsObj.value).map(
                    ([value, label]) => ({
                        value,
                        label,
                    })
                )
                otherTableColumn.value = arr.filter((item, index) => {
                    if (showField.value.includes(item.value)) return item
                })
                console.log(
                    '[ otherTableColumn.value  ] >',
                    otherTableColumn.value
                )
            })
        }
        // 日期选择  ⬇️
        const cascaderStartDate = computed(() => {
            return stationInfo.createTime || '1971-01-01'
        })
        // 这个可以根据站点详情返回的投运日期

        const detaultRangeDate = [
            moment().subtract(2, 'day').format('YYYY-MM-DD'),
            moment().format('YYYY-MM-DD'),
        ]
        const rangeDate = ref({
            startDate: detaultRangeDate[0],
            endDate: detaultRangeDate[1],
        })
        const onDateChange = async () => {
            otherTableData.value = []
            allOtherTableData.value = []
            showMore.value = undefined
            tableLoading.value = true
            if (pageInfo.value.current == 1) {
                await onSearchOther()
            } else {
                pageInfo.value.current = 1
            }
            tableLoading.value = false
        }
        const showMore = ref(undefined)
        const onSearchOther = async (flag) => {
            tableLoading.value = true
            await getEmsDataColumn(
                ['gridmeter', 'bmsmeter'].includes(deviceType.value)
                    ? 'DB'
                    : deviceType.value,
                flag
            )
            const arr = Object.entries(filedsObj.value).map(
                ([value, label]) => ({
                    value,
                    label,
                })
            )
            showFields.value = arr
            showField.value = arr
                .filter((item, index) => {
                    if (index < 30) return item.value
                })
                .map((item) => item.value)
            otherTableColumn.value = arr.filter((item, index) => {
                if (index < 30) return item.value
            })
            let params = {
                stationNo: route.query.stationNo,
                deviceSn: activeDevice.value,
                deviceType: deviceType.value,
                startDate: rangeDate.value.startDate,
                endDate: rangeDate.value.endDate,
                ...pageInfo.value,
                nextToken: showMore.value,
            }
            const res = await carApi.getVehicleBmsDataLog(params)
            //   根据选择的字段进行筛选
            if (res.data.data) {
                pageTotal.value = res.data.data.total
                allOtherTableData.value = allOtherTableData.value.concat(
                    res.data.data.records
                )
                showMore.value = res.data.data.nextToken || undefined
                await onShowFieldChange()
            } else {
                // pageInfo.value.current = current - 1
            }
            tableLoading.value = false
        }
        const loadMoreLoading = ref(false)
        const loadMore = async () => {
            loadMoreLoading.value = true
            await onSearchOther()
            loadMoreLoading.value = false
        }
        return {
            getCompanyInfo,
            // descriptData,
            tabList,
            tabIndexOne,
            tabIndexTwo,
            stationInfo,
            customerDetail,
            dayjs,
            pieceOthenData,
            chargeStatisticsData,
            unitConversion,
            alternateUnits,
            efficiencySelect,
            deviceChargeDateChange,
            deviceList,
            activeDeviceInfo,
            bmsInfoData,
            cellData,
            voltageTime,
            electricalTime,
            voltageChange,
            voltageClick,
            electricaClick,
            pickerTime,
            pickChange,
            electricalChange,
            electricLoading,
            voltageLoading,
            goRouter,
            defaultImg,
            // new
            getState,
            changeTab,
            activeKey,

            // 收益
            // 异常
            getAlarmDataFlag,

            // 分析
            //
            infoVisible,
            handleEditInfo,
            onClose,
            onUpdate,
            deviceInfo,

            //
            loading,
            //
            // 新日期选择框
            chargeDateSearchChange,
            lookDetail,
            zhCn,
            chargeDateSelect,
            confirmExportProfit,
            getSegmentTypeColor,
            moment,

            // 车辆新数据
            activeDevice,
            handleDeviceTabChange,
            showBmsBox,
            lookBattery,
            rankList,
            chargeType,
            toggleRank,
            statusData,
            SOCData,
            OffDevice,
            lookHistoryData,
            handleRefresh,
            editDevice,
            editDeviceVisible,
            alarmData,
            profitUnit,
            onUpdateDevice,
            selectSupplierInfo,
            chargeViewType,
            onChangeView,
            chargeTableData,

            onChangeDeviceDataView,
            deviceDataViewType,
            deviceType,
            types,
            deviceTypes,
            showField,
            showFields,
            onTypeChange,
            onShowFieldChange,
            rangeDate,
            cascaderStartDate,
            onDateChange,
            tableLoading,
            pageInfo,
            pageTotal,
            otherTableData,
            otherTableColumn,
            showMore,
            loadMoreLoading,
            loadMore,
            t,
        }
    },
}
</script>

<style scoped lang="less">
.device_detail {
    padding-top: 88px;
    .class-bt {
        height: 32px;
        font-size: 14px;
        padding: 4px 10px;
        color: #141414;
        font-family: AlibabaPuHuiTi_2_55_Regular;
        border: 1px solid transparent;

        i {
            font-size: 18px;
        }

        :deep(.icons) {
            width: 18px;
            height: 18px;
            margin-right: 3px;
        }

        :deep(.icon-svg-box) {
            margin-right: 5px;
        }

        // &:hover {
        //     background-color: var(--themeColor);
        //     color: #fff;
        //     :deep(.icons) {
        //         color: #fff;
        //     }
        // }
    }

    .border-1 {
        padding: 4px 15px;
        font-size: 14px;
        font-family: AlibabaPuHuiTi_2_55_Regular;
        height: 32px;

        &:hover {
            color: var(--themeColor);
            border-color: var(--themeColor);
        }

        &:focus {
            color: var(--themeColor) !important;
            border-color: var(--themeColor) !important;
        }
    }

    .my-m-l-1 {
        margin-left: 4px;
    }

    .go-box {
        font-family: AlibabaPuHuiTi_2_65_Medium;
        color: rgba(34, 34, 34, 0.6);

        .bt-box-go {
            display: inline-block;
            width: 32px;
            height: 32px;
            line-height: 32px;
            background-color: #fff;
            text-align: center;

            &:hover {
                background-color: var(--themeColor);
                color: #fff;
            }
        }
    }

    .blockTabsb {
        background-color: #fff;
        box-sizing: border-box;
        padding: 0 1rem;

        & :deep(.ant-tabs-tab-active) {
            color: var(--themeColor);
            // background: #fff;
        }

        & :deep(.ant-tabs-ink-bar) {
            width: 32px !important;
            height: 3px;
            background-color: var(--themeColor) !important;
            // left: 50%;
            margin-left: 16px;
        }

        :deep(.ant-tabs-bar) {
            border-bottom: 0 !important;
            margin-bottom: 16px;
        }

        :deep(.ant-tabs-nav) {
            width: 100%;
        }

        :deep(.ant-tabs-content) {
            flex: 1;
        }

        :deep(.ant-tabs-tab) {
            font-family: AlibabaPuHuiTi_2_65_Medium;
            margin: 0px;
            padding: 6px;
            font-size: 14px;
            border-radius: 4px;

            // background: #f5f5f5;
            &:hover {
                color: var(--themeColor);
            }
        }
    }

    .translateTabs {
        padding: 0 !important;
    }

    .tab-content {
        border-radius: 4px;
        border: 1px solid var(--border);
        overflow: hidden;

        .translate-svg {
            position: relative;
            height: 100%;

            &::after {
                content: '';
                position: absolute;
                right: 0;
                top: 50%;
                transform: translateY(-50%);
                height: 80%;
                width: 1px;
                background: var(--border);
            }
        }
    }

    .bg-title-t {
        background-color: var(--bg-f5);
        color: #222;
        font-size: 0.75rem;
        padding: 10px;
    }

    .origin {
        display: inline-block;
        width: 6px;
        height: 6px;
        background-color: rgba(51, 190, 79, 1);
        vertical-align: middle;
        border-radius: 50%;
    }

    .bt-box {
        :deep(.translate_detail) {
            .bg-title-t {
                background-color: #fff !important;
            }
        }

        .bg-bt {
            // background: rgba(245, 247, 247, 1);
            border-radius: 8px;
        }
    }

    .raduis-box {
        display: inline-block;
        width: 10px;
        height: 10px;
        background: #3ecdda;
    }

    :deep(.ant-select-focused) {
        .ant-select-selector {
            border-color: var(--themeColor) !important;
            box-shadow: none !important;
        }
    }

    .w40 {
        width: 160px;
        font-size: 14px;

        :deep(.ant-select) {
            font-size: 14px;
        }

        :deep(.ant-select-selector) {
            height: 32px;
            padding: 0 11px;

            .ant-select-selection-search-input {
                height: 30px;
            }

            .ant-select-selection-item {
                line-height: 30px;
                padding-right: 18px;
            }

            .ant-select-selection-placeholder {
                line-height: 30px;
            }
        }

        :deep(.ant-select-arrow) {
            right: 11px;
            width: 12px;
            height: 12px;
            margin-top: -6px;
            font-size: 12px;
        }

        :hover {
            border-color: var(--themeColor);
        }
    }

    .range-picker {
        width: 240px;

        :deep(.ant-calendar-picker-input) {
            padding: 0px 11px;
            height: 32px;
            box-sizing: border-box;
            display: flex;

            .ant-calendar-range-picker-input {
                font-size: 14px;
                width: 90px;
                text-align: center;
            }

            .ant-calendar-picker-icon {
                right: 5px;
                // font-size: 14px;
                // top: 57%;
            }

            .ant-calendar-range-picker-separator {
                min-width: 30px;
                line-height: 30px;
                // vertical-align: middle !important;
            }
        }

        :deep(.ant-input) {
            &:focus {
                border-color: var(--themeColor);
                box-shadow: none;
            }
        }

        :hover {
            border-color: var(--themeColor);
        }
    }

    :deep(.ant-calendar-picker) {
        &:focus {
            .ant-calendar-picker-input {
                &:not(.ant-input-disabled) {
                    border-color: var(--themeColor) !important;
                    box-shadow: none;
                }
            }
        }
    }

    .date-picker {
        width: 150px;

        :deep(.ant-calendar-picker-input) {
            font-size: 14px;

            &:hover {
                border-color: var(--themeColor);
            }
        }

        :deep(.ant-calendar-picker-icon) {
            // right: 12px;
            // width: 14px;
            // height: 14px;
            // display: flex;
            // transform: translatey(-50%);
            svg {
                width: 14px !important;
                height: 14px !important;
            }
        }
    }

    .my-rounded-lg {
        border-radius: 8px;
    }

    .icon-svg-box {
        width: 16px;
        height: 17.6px;
    }

    .flex-1-width {
        width: 618px;
    }
}

:deep(.topTabs .ant-tabs-nav) {
    display: none !important;
}

:deep(.ant-tabs-bar) {
    border: 0;
    margin-bottom: 0;
}

.device-tabs {
    padding: 16px 16px 0 16px;
}

:deep(.device-tabs .ant-tabs-nav) {
    display: inline-block !important;
    width: auto !important;
    background: var(--bg-f5);
    border-radius: 8px;
    padding: 4px;
}

//
.detail-info {
    // background: linear-gradient(180deg, #f6f6f6 0%, #ffffff 100%);
    background: #fff;
    border-radius: 8px;
    // backdrop-filter: blur(10px);
    border: 1px solid var(--border);
    border-radius: 8px;

    // border: 2px solid #ffffff;
    // background: #fff;
}

.alarm-item {
    height: 82px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    flex: 1;
    background: #f4f5f9;
    border-radius: 6px;
}

.tabs-box {
    position: relative;
    margin-bottom: 18px;
    transition: all 0.3s;

    &::after {
        display: none;
        content: '';
        width: 0;
        height: 0;
        border-top: 9px solid var(--car-pie-border);
        border-left: 12px solid transparent;
        border-right: 12px solid transparent;
        border-bottom: 1px solid transparent;
        position: absolute;
        left: 50%;
        margin-left: -6px;
        margin-top: -0;
        top: 100%;
    }

    &.active {
        box-shadow: 0px 2px 10px 0px rgba(34, 34, 34, 0.2);

        &::after {
            display: block;
        }
    }

    &:hover {
        box-shadow: 0px 2px 10px 0px rgba(34, 34, 34, 0.2);
    }
}

.electricity {
    height: 522px;
    // background: #ffffff;
    column-gap: 14px;

    .voltage {
        height: 388px;
    }

    .electric {
        height: 388px;
    }

    .my-tab {
        display: flex;
        flex-wrap: wrap;
        column-gap: 24px;
        padding: 0 10px;

        .tab-item {
            padding: 23px 0 8px 0;
            cursor: pointer;
            position: relative;
            margin-bottom: 12px;
            font-size: 14px;
            color: var(--text-60);
        }

        .active-tab-item {
            &::after {
                content: '';
                position: absolute;
                bottom: 0;
                width: 24px;
                left: 50%;
                transform: translateX(-50%);
                height: 3px;
                background-color: var(--themeColor);
            }
        }
    }

    .tab-select {
        display: flex;
        align-items: center;
        padding-right: 10px;
    }
}

.cabinet {
    .se701 {
        width: 202px;
        height: 310px;
        position: relative;

        .se70-box {
            width: 100%;
            height: 100%;
            position: absolute;
            left: 0;
            top: 0;
            z-index: 3;

            .se70-bg {
                width: 100%;
                height: 100%;
                position: absolute;
                left: 0;
                top: 0;
                z-index: 2;
                cursor: pointer;
                border: 2px solid transparent;

                .block0 {
                    position: absolute;
                    z-index: 8;
                }

                .block1 {
                    width: calc(~'100% - 10px');
                    height: 18px;

                    left: 5px;
                    top: 0;
                }

                .block2 {
                    width: 5px;
                    height: 100%;

                    left: 0;
                    top: 0;
                }

                .block25 {
                    width: 4px;
                    height: 42px;
                    left: 5px;
                    top: 18px;
                }

                .block3 {
                    width: 5px;
                    height: 100%;

                    right: 0;
                    top: 0;
                }

                .block4 {
                    width: 97px;
                    height: 42px;

                    left: 99px;
                    top: 18px;
                }

                .block5 {
                    width: calc(~'100% - 10px');
                    height: 14px;

                    left: 5px;
                    top: 60px;
                }

                .block6 {
                    width: calc(~'100% - 10px');
                    height: 10px;

                    left: 5px;
                    bottom: 0;
                }

                &:hover {
                    // background: rgba(111, 190, 206, 0.5);
                    border: 2px solid var(--themeColor);
                }

                &.active {
                    // background: rgba(111, 190, 206, 0.5);
                    border: 2px solid var(--themeColor);

                    .block0 {
                        background: rgba(111, 190, 206, 0.5);
                    }
                }
            }

            .pcs {
                width: 88px;
                height: 40px;
                border-radius: 4px;
                cursor: pointer;
                margin-top: 21px;
                margin-left: 12px;
                position: relative;
                z-index: 3;
                border: 2px solid transparent;

                &:hover {
                    // background: rgba(111, 190, 206, 0.5);
                    border: 2px solid var(--themeColor);
                }

                &.active {
                    background: rgba(111, 190, 206, 0.5);
                    border: 2px solid var(--themeColor);
                }
            }

            .battery-main {
                width: 192px;
                height: 224px;
                left: 0;
                top: 0;
                cursor: pointer;
                z-index: 4;
                position: absolute;
                border: 2px solid transparent;

                &:hover {
                    // background: rgba(111, 190, 206, 0.5);
                    border: 2px solid var(--themeColor);
                }

                &.active {
                    background: rgba(111, 190, 206, 0.5);
                    border: 2px solid var(--themeColor);
                }
            }

            .batterys {
                width: 192px;
                margin-top: 14px;
                margin-left: 5px;
                padding: 10px;
                display: flex;
                flex-wrap: wrap;
                column-gap: 10px;
                border-radius: 4px;
                position: absolute;
                z-index: 5;

                .battery {
                    width: 80px;
                    height: 32px;
                    cursor: pointer;
                    position: relative;
                    margin-bottom: 11px;
                    z-index: 6;

                    .detail {
                        width: 100%;
                        height: 100%;
                        border-radius: 4px;
                        border: 2px solid transparent;

                        &:hover {
                            // background: rgba(111, 190, 206, 0.5);
                            border: 2px solid var(--themeColor);
                        }

                        &.active {
                            background: rgba(111, 190, 206, 0.5);
                            border: 2px solid var(--themeColor);
                        }
                    }
                }
            }
        }
    }

    .se702 {
        width: 202px;
        height: 310px;
        position: relative;

        .se70-box {
            width: 100%;
            height: 100%;
            position: absolute;
            left: 0;
            top: 0;
            z-index: 3;
        }

        .se70-bg {
            width: 100%;
            height: 100%;
            position: absolute;
            left: 0;
            top: 0;
            z-index: 2;
            // cursor: pointer;
            border: 2px solid transparent;

            // &:hover {
            // background: rgba(111, 190, 206, 0.5);
            // border: 2px solid var(--themeColor);
            // }
            &.active {
                background: rgba(111, 190, 206, 0.5);
                border: 2px solid var(--themeColor);
            }
        }

        .ammeter {
            width: 52px;
            height: 25px;
            margin-left: 45px;
            margin-top: 33px;
            cursor: pointer;
            position: relative;
            z-index: 3;
            border: 2px solid transparent;

            &:hover {
                // background: rgba(111, 190, 206, 0.5);
                border: 2px solid var(--themeColor);
            }

            &.active {
                background: rgba(111, 190, 206, 0.5);
                border: 2px solid var(--themeColor);
            }
        }

        .dynamicRing {
            width: 140px;
            height: 180px;
            margin-top: 16px;
            margin-left: 31px;
            cursor: pointer;
            position: relative;
            z-index: 3;
            border: 2px solid transparent;
            display: flex;
            justify-content: center;
            align-items: center;

            &:hover {
                // background: rgba(111, 190, 206, 0.5);
                border: 2px solid var(--themeColor);
            }

            &.active {
                background: rgba(111, 190, 206, 0.5);
                border: 2px solid var(--themeColor);
            }
        }
    }
}

:deep(.ant-switch) {
    min-width: 40px;
    height: 24px;
}

.ant-switch-loading-icon,
.ant-switch::after {
    width: 20px;
    height: 20px;
}

:deep(.ant-switch-checked) {
    background-color: var(--themeColor);
}
:deep(.more-icon) {
    width: 20px;
    height: 20px;
}
.dark .device_detail .blockTabsb {
    background-color: transparent;
}
.tabs {
    position: relative;

    &::before {
        display: block;
        content: '';
        border-bottom: 4px solid rgba(34, 34, 34, 0.04);
        border-left: 4px solid transparent;
        border-right: 4px solid transparent;
        position: absolute;
        left: 278px;
        bottom: 100%;
    }
}

:deep(.el-tabs__nav) {
    padding: 4px;
    // background: var(--bg-f5);
    border-radius: 4px;
    border: 1px solid rgba(217, 217, 217, 0.4);
}

:deep(.el-tabs__nav-wrap:after) {
    display: none;
}

:deep(.el-tabs__item) {
    color: var(--text-100) !important;
    font-weight: normal;
    line-height: 30px;
    height: 30px;
    padding: 0 6px;
}

:deep(.el-tabs__item.is-active) {
    color: var(--themeColor) !important;
}

:deep(.el-tabs__item.is-active) {
    // background: #fff;
    // padding: 0 20px;
    border-radius: 4px !important;
    text-align: center;
    font-weight: 500;

    &::after {
        content: '';
        display: block;
        position: absolute;
        bottom: 0;
        left: 6px;
        width: calc(~'100% - 12px');
        height: 4px;
        background: var(--themeColor);
    }
}

:deep(.el-tabs--bottom) {
    .el-tabs__item.is-bottom:last-child {
        padding: 0 6px;
    }

    .el-tabs__item.is-top:last-child {
        padding: 0 6px;
    }

    .el-tabs__item.is-bottom:nth-child(2) {
        padding: 0 6px;
    }

    .el-tabs__item.is-top:nth-child(2) {
        padding: 0 6px;
    }
}

:deep(.el-tabs--top) {
    .el-tabs__item.is-bottom:last-child {
        padding: 0 6px;
    }

    .el-tabs__item.is-top:last-child {
        padding: 0 6px;
    }

    .el-tabs__item.is-bottom:nth-child(2) {
        padding: 0 6px;
    }

    .el-tabs__item.is-top:nth-child(2) {
        padding: 0 6px;
    }
}

:deep(.el-tabs__active-bar) {
    display: none;
}

.device-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 4px;
    border: 1px solid var(--border);
    // background: #fff;
    padding: 14px 16px;
    margin-bottom: 12px;
}

.device-img {
    width: 46.76%;
    height: 370px;
}

.device-info {
    width: 53%;

    .content-desc {
        height: 325px;
    }

    .cell {
        height: 325px;
    }
}

:deep(.el-tabs__header) {
    margin-bottom: 0;
}

.rank {
    width: 386px;
    background: var(--bg-f5);
    border-radius: 6px;
    padding: 20px;

    .rank-num {
        width: 32px;
        text-align: center;
        margin-right: 10px;
    }

    .rank-name {
        width: 120px;
        text-align: left;
    }

    .rank-process {
        flex: 1;

        .quantity {
            font-weight: 500;
            font-size: 18px;
        }
    }

    .toggle {
        width: 16px;
        height: 16px;
    }
}
</style>

<style lang="less">
.ant-calendar-today .ant-calendar-date {
    color: var(--themeColor);
    border-color: var(--themeColor);
}

.ant-calendar-selected-day .ant-calendar-date {
    background: var(--themeColor);
    border: 1px solid transparent;
    color: #fff;
}

.ant-calendar-date:active {
    background: var(--themeColor);
    color: #fff;
}

.ant-calendar-selected-date .ant-calendar-date {
    color: #fff;
    background-color: var(--themeColor);
}

.ant-calendar-range .ant-calendar-selected-start-date .ant-calendar-date {
    background-color: var(--themeColor);
}

.ant-calendar-range .ant-calendar-selected-start-date .ant-calendar-date:hover {
    background-color: var(--themeColor);
}

.ant-calendar-range .ant-calendar-selected-end-date .ant-calendar-date:hover {
    background-color: var(--themeColor);
}

.ant-calendar-range .ant-calendar-selected-end-date .ant-calendar-date {
    background-color: var(--themeColor);
}

.ant-calendar-range .ant-calendar-selected-start-date .ant-calendar-date {
    background-color: var(--themeColor);
}

.ant-calendar-range .ant-calendar-in-range-cell::before {
    background: #f5f5f5;
}

.ant-calendar-picker .ant-calendar-picker-icon {
    margin-top: -8px;
}

.ant-calendar-picker .ant-calendar-picker-clear {
    display: none;
}

@keyframes changeWidth {
    0% {
        width: 0;
    }

    100% {
        width: 100%;
    }
}

.charge-title-l,
.charge-title-r {
    width: calc(~'50% - 24px');
    position: relative;
}

.charge-title-l {
    background: rgba(51, 190, 79, 0.1);

    &::after {
        display: block;
        content: '';
        width: 0;
        height: 0;
        left: 100%;
        position: absolute;
        top: 0;
        border-top: 48px solid rgba(51, 190, 79, 0.1);
        border-right: 43px solid transparent;
    }
}

.charge-title-r {
    background: rgba(119, 155, 219, 0.1);

    &::after {
        display: block;
        content: '';
        width: 0;
        height: 0;
        right: 100%;
        position: absolute;
        top: 0;
        border-bottom: 48px solid rgba(119, 155, 219, 0.1);
        border-left: 43px solid transparent;
    }
}
.el-select-dropdown__item {
    color: var(--text-100);
}
.el-select-dropdown__item.is-selected {
    color: var(--themeColor);
    background-color: var(--selected-color);
}
.el-select-dropdown__item.is-hovering {
    color: var(--themeColor);
    background-color: var(--selected-color);
}
.el-popper.is-light {
    background: #fff;
}
.dark {
    .el-popper {
        border-color: transparent;
    }
    .el-popper.is-light {
        background: var(--main-bg);
    }
}
.box-border {
    border: 1px solid var(--border);
}
</style>
