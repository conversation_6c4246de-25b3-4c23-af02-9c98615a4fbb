// utils/convertTableHeaders.js
import moment from 'moment'
import { getState } from '@/common/util.js'
const formatterStatus = (e) => {
  return t(getState(e.chargeStatus, 'power').label)
}
const DEFAULT_WIDTH = 150

// 组配置：字段归属哪个分组（用于 headerGroups）
const groupMap = {
  '系统信息': ['sysVoltage', 'sysCurrent', 'soc', 'soh'],
  '故障信息': ['faultCode', 'faultFlag1', 'faultFlag2'],
}

// 固定字段配置
function fixedFieldMap(t, locale) {
  return {
    time: {
      value: 'time',
      title: t('Collection time'),
      key: 'time',
      dataKey: 'time',
      width: 200,
      cellRenderer: ({ cellData: time }) =>
        moment(new Date(time * 1000)).format(
          'YYYY-MM-DD HH:mm:ss'
        ),
    },

    status: {
      value: 'status',
      title: t('Charging and discharging status'),
      key: 'status',
      dataKey: 'status',
      width: locale == 'en' ? 240 : 150,
      cellRenderer: ({ cellData: status }) => {
        return formatterStatus({ chargeStatus: status })
      },
    },
    alarmSta: {
      value: 'alarmSta',
      title: t('Alarm status'),
      key: 'alarmSta',
      dataKey: 'alarmSta',
      width: 120,
      cellRenderer: ({ cellData: alarmSta }) => {
        return alarmSta == 1 ? ' 异常' : '正常'
      },
    },
    lockSta: {
      value: 'lockSta',
      title: t('Lock status'),
      key: 'lockSta',
      dataKey: 'lockSta',
      width: 150,
      cellRenderer: ({ cellData: lockSta }) => {
        return lockSta == 0
          ? t('status_zhengchang')
          : t('locked')
      }, // 1:锁住 0:未锁
    },
  }
}

// 动态字段合成逻辑
export function convertTableHeaders(data, t, locale) {
  const fixedFields = fixedFieldMap(t, locale)
  const fixedKeys = Object.keys(fixedFields)

  // 合并字段列表
  const columns = []

  // 加入固定字段
  fixedKeys.forEach((key) => {
    columns.push(fixedFields[key])
  })

  // 加入接口字段（跳过已包含的固定字段）
  Object.entries(data).forEach(([key, title]) => {
    if (!fixedKeys.includes(key)) {
      columns.push({
        value: key,
        title,
        key,
        dataKey: key,
        width: DEFAULT_WIDTH,
      })
    }
  })

  // 分组 headerGroups 处理
  const headerGroups = []
  const usedGroupKeys = new Set()

  Object.entries(groupMap).forEach(([groupTitle, fieldKeys]) => {
    const children = columns.filter((col) => fieldKeys.includes(col.key))
    if (children.length > 0) {
      headerGroups.push({
        title: groupTitle,
        children,
      })
      children.forEach((col) => usedGroupKeys.add(col.key))
    }
  })

  // 剩余字段作为“未分组”直接加入
  const flatColumns = columns.filter((col) => !usedGroupKeys.has(col.key))
  const finalColumns = headerGroups.length > 0 ? headerGroups.concat(flatColumns) : columns

  return {
    columns: finalColumns,
  }
}
