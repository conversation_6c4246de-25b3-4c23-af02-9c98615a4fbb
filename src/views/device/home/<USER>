<template>
    <div>
        <div class="flex gap-x-6">
            <div class="" style="width: 634px">
                <div class="mb-4">收益排名</div>
                <div
                    class="bg-f5f7f7 dark:bg-ffffff-dark rounded px-1.5 py-1 flex leading-5.5"
                >
                    <div class="rank-header text-center py-2.5">
                        <div class="mb-3 h-5.5">排名</div>
                        <div class="mb-7">站点</div>
                        <div>
                            收益
                            <br />
                            ({{ profitUnit }})
                        </div>
                    </div>
                    <div class="flex-1 flex items-center px-3 w-0">
                        <template
                            v-for="(item, index) in dataSource"
                            :key="index"
                        >
                            <div class="w-1/5 pr-1.5" v-if="index < 5">
                                <div class="mb-3 h-5.5">{{ index + 1 }}</div>
                                <div
                                    class="mb-7 overflow"
                                    :title="item.stationName"
                                >
                                    {{ item.stationName }}
                                </div>
                                <div class="h-11">
                                    <div class="text-lg leading-5.5">
                                        {{ item.profit }}
                                    </div>
                                    <div>
                                        <BarProgress
                                            :fullWidth="100"
                                            :ratio="item.ratio"
                                        />
                                    </div>
                                </div>
                            </div>
                        </template>
                        <template v-if="!dataSource?.length">
                            <empty-data
                                :description="'暂无数据'"
                                class="mx-auto"
                            >
                                <slot name="empty"></slot>
                            </empty-data>
                        </template>
                    </div>
                </div>
            </div>
            <div class="flex-1">
                <div class="mb-4">
                    社会贡献<a-tooltip
                        title="此类数据指标整体测算逻辑基于系统利润、节省电量等基本数据，结合国家碳排放因子等参数，测算出了储能系统在节能减排方面的量化贡献。"
                    >
                        <QuestionCircleOutlined />
                    </a-tooltip>
                </div>
                <div class="flex justify-between gap-x-2">
                    <div class="flex-1 p-5 h-40 flex-item1">
                        <div class="flex-box">
                            <div class="felx-item-top">
                                <span
                                    class="flex-number text-3.5xl leading-10 mb-1 font-bold"
                                    >{{
                                        kgUnitg(
                                            socialContribution.savingCarbonEmission
                                        )
                                    }}</span
                                >
                                <span
                                    class="flex-unit text-secondar-text dark:text-60-dark ml-2"
                                ></span>
                            </div>
                            <div class="felx-item-bottom">
                                <span class="flex-title"
                                    >节约标准煤({{
                                        isUnits(
                                            socialContribution?.savingCarbonEmission
                                        )
                                    }})</span
                                >
                            </div>
                        </div>
                    </div>
                    <div class="flex-1 p-5 h-40 flex-item2">
                        <div class="flex-box">
                            <div class="felx-item-top">
                                <span
                                    class="flex-number text-3.5xl leading-10 mb-1 font-bold"
                                    >{{
                                        kgUnitg(
                                            socialContribution?.savingCarbonDioxideEmission
                                        )
                                    }}</span
                                >
                                <span
                                    class="flex-unit text-secondar-text dark:text-60-dark ml-2"
                                ></span>
                            </div>
                            <div class="felx-item-bottom">
                                <span class="flex-title"
                                    >CO₂减排量({{
                                        // : 'g'
                                        isUnits(
                                            socialContribution?.savingCarbonDioxideEmission
                                        )
                                    }})</span
                                >
                            </div>
                        </div>
                    </div>
                    <div class="flex-1 p-5 h-40 flex-item3">
                        <div class="flex-box">
                            <div class="felx-item-top">
                                <span
                                    class="flex-number text-3.5xl leading-10 mb-1 font-bold"
                                    >{{
                                        socialContribution?.equivalentTreesQuantity ||
                                        socialContribution?.equivalentTreesQuantity ==
                                            0
                                            ? socialContribution.equivalentTreesQuantity
                                            : '-'
                                    }}</span
                                >
                                <span
                                    class="flex-unit text-secondar-text dark:text-60-dark ml-2"
                                >
                                </span>
                            </div>
                            <div class="felx-item-bottom">
                                <span class="flex-title"
                                    >等效植树量({{
                                        socialContribution?.equivalentTreesQuantity ||
                                        socialContribution?.equivalentTreesQuantity ==
                                            0
                                            ? '颗'
                                            : ''
                                    }})</span
                                >
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { QuestionCircleOutlined } from '@ant-design/icons-vue'
import BarProgress from '../components/barProgress.vue'
import { ref, watch } from 'vue'

import { someMax, kgUnitg, isUnits, roundNumFun } from '../const'
export default {
    components: {
        QuestionCircleOutlined,
        BarProgress,
    },
    props: {
        dataSource: {
            type: Array,
            default: () => [],
        },
        socialContribution: {
            type: Object,
            default: () => ({}),
        },
        profitUnit: {
            type: String,
            default: () => '元',
        },
    },
    setup(props) {
        const profits = ref([])
        watch(
            () => props.dataSource,
            (val) => {
                if (val) {
                    profits.value = val.filter((item) => {
                        return item.profit > 10000
                    })

                    const isBoolean = profits.value.length
                }
            },
            { immediate: true }
        )
        return {
            someMax,
            kgUnitg,
            isUnits,
            profits,
        }
    },
}
</script>

<style lang="less" scoped>
.rank-header {
    width: 62px;
    background: linear-gradient(270deg, #ffffff 0%, #f6f6f6 100%);
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    border: 2px solid #ffffff;
}
.flex-item1 {
    background-image: url('../../../assets/device/bg-3.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
}

.flex-item2 {
    background-image: url('../../../assets/device/bg-2.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
}

.flex-item3 {
    background-image: url('../../../assets/device/bg-1.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
}
</style>
