<template>
    <div class="enterprise-detail-top">
        <div class="enterprise-detail">
            <div class="enterprise-detail-l">
                <div class="detail-title">{{ $t('enterprise_qiyexinxi') }}</div>
                <div class="enterprise-detail-nameBox flex">
                    <span class="nameBox">{{
                        $t('enterprise_qiyemingcheng')
                    }}</span
                    ><span class="detail-content">
                        <a-input
                            v-model:value="currentOrgData.orgName"
                            v-show="show"
                        />
                        <span v-show="!show">
                            {{ currentOrgData?.orgName || '-' }}
                        </span>
                    </span>
                </div>
                <div class="enterprise-admin enterprise-detail-nameBox flex">
                    <span class="nameBox">{{
                        $t('enterprise_zhuguanliyuan')
                    }}</span
                    ><span class="detail-content">
                        <a-select
                            v-model:value="currentOrgData.orgOwnerId"
                            :options="options"
                            v-show="show"
                            class="w40"
                            @change="selectChange"
                        />
                        <span v-show="!show">
                            {{
                                currentOrgData?.realName
                                    ? currentOrgData.realName + ' | '
                                    : '-'
                            }}{{ currentOrgData?.phone || '' }}
                        </span>
                    </span>
                </div>
                <p class="illustrate enterprise-detail-nameBox">
                    {{ $t('enterprise_tips001') }}
                </p>
            </div>
            <div class="enterprise-default">
                <div class="default-title">
                    {{ $t('enterprise_qiyezhuti') }}
                </div>
                <div class="flex items-center item-box">
                    <div class="title">{{ $t('enterprise_qiyeyanse') }}</div>
                    <div>
                        <div class="relative relative-box flex items-center">
                            <div
                                plain
                                round
                                @click="pickClick"
                                :style="{
                                    backgroundColor: currentOrgData.themeColor,
                                    borderColor: currentOrgData.themeColor,
                                }"
                                style="
                                    width: 120px;
                                    height: 32px;
                                    border-radius: 999px;
                                    cursor: pointer;
                                "
                            ></div>

                            <div class="inline-flex items-center inline-box">
                                <span>{{
                                    $t('enterprise_dangqiansezhi')
                                }}</span>
                                <a-input
                                    v-model:value="currentOrgData.themeColor"
                                    v-show="show"
                                />
                                <span v-show="!show">{{
                                    currentOrgData.themeColor
                                }}</span>
                            </div>
                            <div v-show="isShow" class="absolute absolute-box">
                                <Vue3ColorPicker
                                    v-model:pureColor="
                                        currentOrgData.themeColor
                                    "
                                    pickerType="chrome"
                                    :isWidget="isShow"
                                    format="hex"
                                    :zIndex="1"
                                    :disableHistory="true"
                                    :roundHistory="true"
                                    :disableAlpha="true"
                                    @pureColorChange="handlePureColorChange"
                                />
                                <div class="picker-bt">
                                    <el-button
                                        plain
                                        round
                                        @click="isShow = false"
                                        >{{ $t('common_guanbi') }}</el-button
                                    >
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <p class="p-title">（{{ $t('enterprise_tips002') }}）</p>
                <div class="item-box flex items-center">
                    <div class="title">{{ $t('enterprise_qiyelogo') }}</div>
                    <div class="flex items-center">
                        <div class="logo-box" v-show="!show">
                            <div
                                class="flex items-center justify-center h-full border border-solid border-d9"
                            >
                                <img
                                    :src="
                                        currentOrgData.orgLogoFile.length > 0
                                            ? currentOrgData.orgLogoFile[0].url
                                            : null
                                    "
                                />
                            </div>
                        </div>
                        <div v-show="show" style="width: 160px; height: 92px">
                            <Upload
                                :data="{ scene: 'orgLogo' }"
                                :className="'orgLogo'"
                                @success="success"
                                v-model:file-list="currentOrgData.orgLogoFile"
                                @delImgFile="delImgFile"
                            />
                        </div>
                        <div class="illustrates box-1" v-show="show">
                            <span>{{ $t('upload_chicunyaoqiu') }}： </span>
                            <p>
                                {{ $t('upload_chang') }}：∞，{{
                                    $t('upload_kuan')
                                }}：40px， {{ $t('upload_tips001') }}1M
                            </p>
                        </div>
                    </div>
                </div>

                <div class="item-box flex items-center">
                    <div class="title">
                        {{ $t('enterprise_dengluyebeijing') }}
                    </div>
                    <div class="flex flex-1 items-center w-0">
                        <div class="bg-box" v-if="!show">
                            <img
                                :src="
                                    currentOrgData.loginBannerFile.length > 0
                                        ? currentOrgData.loginBannerFile[0].url
                                        : null
                                "
                            />
                        </div>
                        <div v-if="show" class="flex items-center">
                            <Upload
                                :data="{ scene: 'loginBaner' }"
                                :className="'loginBaner'"
                                @success="success"
                                v-model:file-list="
                                    currentOrgData.loginBannerFile
                                "
                                @delImgFile="delImgFile"
                            />
                        </div>
                    </div>
                    <div class="illustrates box-2" v-if="show">
                        <span>{{ $t('upload_chicunyaoqiu') }}： </span>
                        <p>
                            {{ $t('upload_chang') }}:{{
                                $t('upload_kuan')
                            }}(1920px * 1080px), {{ $t('upload_tips001') }}1M
                        </p>
                    </div>
                </div>

                <div class="item-box flex items-center">
                    <div class="title">{{ $t('enterprise_wangyebiaoti') }}</div>
                    <div class="flex-1 leading-8">
                        <div class="title-name-box">
                            <a-input
                                v-model:value="currentOrgData.webPageTitle"
                                v-show="show"
                                :maxLength="40"
                            />
                            <span v-show="!show">{{
                                currentOrgData.webPageTitle
                            }}</span>
                        </div>
                    </div>
                </div>

                <div class="item-box flex items-center">
                    <div class="title">{{ $t('enterprise_wangyeicon') }}</div>
                    <div class="flex">
                        <div class="icon-box" v-if="!show">
                            <div
                                class="flex items-center justify-center w-full h-full"
                            >
                                <img
                                    :src="
                                        currentOrgData.webPageIconFile.length >
                                        0
                                            ? currentOrgData.webPageIconFile[0]
                                                  .url
                                            : null
                                    "
                                />
                            </div>
                        </div>
                        <div v-if="show" class="flex items-center">
                            <Upload
                                :data="{ scene: 'icon' }"
                                :className="'icons-up'"
                                @success="success"
                                v-model:file-list="
                                    currentOrgData.webPageIconFile
                                "
                                @delImgFile="delImgFile"
                            />
                        </div>
                    </div>
                    <div class="illustrates box-3" v-show="show">
                        <span>{{ $t('upload_chicunyaoqiu') }}： </span>
                        <p>
                            {{ $t('upload_chang') }}:{{
                                $t('upload_kuan')
                            }}(32px * 32px), {{ $t('upload_tips001') }}1M
                        </p>
                    </div>
                </div>

                <div class="item-box flex">
                    <div class="title leading-8" style="line-height: 32px">
                        {{ $t('enterprise_wangyeyuming') }}
                    </div>
                    <div class="flex-1">
                        <div class="url-box">
                            <span class="input-box" v-show="show">
                                <a-input
                                    v-model:value="currentOrgData.subDomain"
                                    @change="subDomainChange"
                                    :maxLength="20"
                                    :placeholder="$t('placeholder_qingshuru')"
                                />
                                <span>-ems.ssnj.com</span>
                            </span>
                            <span v-show="!show">
                                {{ currentOrgData?.subDomain
                                }}{{
                                    currentOrgData?.subDomain
                                        ? '-ems.ssnj.com'
                                        : ''
                                }}
                                <!-- {{
                                currentOrgData?.subDomain
                                    ? currentOrgData.subDomain + '.ems.ssnj.com'
                                    : ''
                            }} -->
                                <!-- {{ currentOrgData.subDomain }} -->
                            </span>
                        </div>
                        <div class="url-box">
                            <span class="go-title" @click="goWord">
                                {{ $t('enterprise_tips003') }}</span
                            >
                        </div>
                    </div>
                </div>
                <!-- <div class="flex items-center item-box">
                    <div class="title">策略托管</div>
                    <div>
                        <div class="relative relative-box flex items-center">
                            <a-switch
                                :disabled="!show"
                                v-model:checked="currentOrgData.strategyStatus"
                            />
                        </div>
                    </div>
                </div>
                <p class="p-title">启用后服务商可协助进行设备运行策略管理</p> -->
            </div>
        </div>
    </div>
</template>

<script setup>
import { reactive, ref } from 'vue'
import apiService from '@/apiService/device'
import Upload from './Upload.vue'
import { message } from 'ant-design-vue'
import { useI18n } from 'vue-i18n'
const { t, locale } = useI18n()
const login = require('@/assets/login/login-bg.jpg')
const icon = require('@/assets/login/icon.png')
const logo = require('@/assets/login/logox1.png')

const currentOrgData = reactive({
    address: void 0,
    createTime: void 0,
    id: void 0,
    industry: void 0,
    orgLogo: void 0,
    orgLogoFile: [],
    orgName: void 0,
    orgType: void 0,
    parentId: void 0,
    phone: void 0,
    realName: void 0,
    subDomain: void 0,
    themeColor: '#6FBECE',
    thirdOrgId: void 0,
    webPageIcon: void 0,
    webPageIconFile: [],
    webPageTitle: void 0,
    loginBanner: void 0,
    loginBannerFile: [],
    orgOwnerId: void 0,
    strategyStatus: false,
})

const isShow = ref(false)
const show = ref(false)

const showClick = (boolean) => {
    show.value = boolean
    // 获取用户列表
    if (boolean) {
        page()
    }
}

const pickClick = () => {
    if (!show.value) return
    isShow.value = !isShow.value
}

const handlePureColorChange = () => {
    isShow.value = false
}

const success = (info) => {
    const {
        scene,
        file: {
            response: {
                data: { fileVisitUrl },
            },
        },
    } = info
    if (scene == 'icon') {
        currentOrgData.webPageIcon = fileVisitUrl ? fileVisitUrl : void 0
        currentOrgData.webPageIconFile = [
            { name: 'icon', url: fileVisitUrl ? fileVisitUrl : void 0 },
        ]
    }

    if (scene == 'orgLogo') {
        currentOrgData.orgLogo = fileVisitUrl ? fileVisitUrl : void 0
        currentOrgData.orgLogoFile = [
            { name: 'orgLogo', url: fileVisitUrl ? fileVisitUrl : void 0 },
        ]
    }

    if (scene == 'loginBaner') {
        currentOrgData.loginBaner = fileVisitUrl ? fileVisitUrl : void 0
        currentOrgData.loginBannerFile = [
            { name: 'loginBaner', url: fileVisitUrl ? fileVisitUrl : void 0 },
        ]
    }
}

const getOrgData = async () => {
    const {
        data: { data },
    } = await apiService.getCurrentOrgData()
    Object.keys(data).forEach((key) => {
        currentOrgData[key] = data[key] ?? void 0
        if (key == 'themeColor') {
            currentOrgData[key] = data[key] || '#6FBECE'
        }
        if (key == 'orgLogo') {
            currentOrgData.orgLogoFile = data[key]
                ? [{ name: 'orgLogo', url: data[key] }]
                : [{ name: 'orgLogo', url: logo, old: true }]
        }

        if (key == 'loginBanner') {
            currentOrgData.loginBannerFile = data[key]
                ? [{ name: 'login', url: data[key] }]
                : [{ name: 'login', url: login, old: true }]
        }

        if (key == 'webPageIcon') {
            currentOrgData.webPageIconFile = data[key]
                ? [{ name: 'webPageIcon', url: data[key] }]
                : [{ name: 'webPageIcon', url: icon, old: true }]
        }

        if (key == 'subDomain') {
            if (data[key]) {
                const index = data[key].indexOf('-ems.ssnj.com')
                if (index != -1) {
                    const str = data[key].slice(0, index)
                    currentOrgData.subDomain = str
                }
            }
        }
    })
}

const options = ref([])

const page = async () => {
    const params = {
        current: 1,
        size: 10000,
    }
    const {
        data: {
            code,
            data: { records },
        },
    } = await apiService.getStaffPage(params)
    if (code === 0) {
        options.value =
            records?.map((item) => {
                return {
                    ...item,
                    label: `${item.realName ? item.realName : ''} ${
                        item.owner ? t('guanliyuan') : ''
                    }`,
                    value: item.staffId,
                }
            }) || []

        const obj = records?.find((item) => item.owner)
        if (obj) {
            currentOrgData.orgOwnerId = obj.staffId
        }
    }
}

const modifyOrgInfo = async () => {
    const params = {
        orgId: currentOrgData.id,
        orgName: currentOrgData.orgName,
        orgOwnerId: currentOrgData.orgOwnerId,
        orgLogo: currentOrgData.orgLogo,
        themeColor: currentOrgData.themeColor,
        webPageIcon: currentOrgData.webPageIcon,
        webPageTitle: currentOrgData.webPageTitle,
        loginBanner: currentOrgData.loginBaner,
        subDomain: currentOrgData.subDomain
            ? currentOrgData.subDomain + '-ems.ssnj.com'
            : '',
    }

    if (!params?.orgName) {
        message.error(t('enterprise_tips004'))
        return
    }
    return await apiService.modifyOrgInfo(params)
}

const selectChange = (val, option) => {
    currentOrgData.realName = option.realName
    currentOrgData.phone = option.phone
}

const subDomainChange = () => {
    currentOrgData.subDomain = currentOrgData.subDomain.replace(
        /[^a-zA-Z]/g,
        ''
    )
    // currentOrgData.subDomain = currentOrgData.subDomain.replace(/[^\a-zA-Z\@\.]/g,'');
}

const delImgFile = (type) => {
    if (type == 'orgLogo') {
        currentOrgData.orgLogoFile = [{ name: 'orgLogo', url: logo, old: true }]
        currentOrgData.orgLogo = ''
    }

    if (type == 'loginBaner') {
        currentOrgData.loginBannerFile = [
            { name: 'login', url: login, old: true },
        ]
        currentOrgData.loginBaner = ''
    }

    if (type == 'icon') {
        currentOrgData.webPageIconFile = [
            { name: 'webPageIcon', url: icon, old: true },
        ]
        currentOrgData.webPageIcon = ''
    }
}

const goWord = () => {
    // window.open(
    //     'https://alidocs.dingtalk.com/i/nodes/jb9Y4gmKWr7wyzb6F5Mpl72zVGXn6lpz'
    // )
    window.open(
        'https://alidocs.dingtalk.com/i/p/Pl2AmoV5q68Xdb9o5pXBWZBMJnBxZm7Z?dontjump=true'
    )
}

defineExpose({ showClick, modifyOrgInfo, getOrgData })
</script>

<style lang="less" scoped>
.enterprise-detail-top {
    width: 660px;
    margin: auto;
    margin-top: 43px;

    .enterprise-detail {
        // padding: 16px;
        // display: flex;
        // background: #f5f7f7;
        border-radius: 8px;

        .enterprise-detail-l {
            // flex: 1;
            font-family: AlibabaPuHuiTi_2_55_Regular;

            .detail-title {
                height: 22px;
                font-size: 14px;
                color: var(--text-40);
                line-height: 22px;
                margin-top: 5px;
                margin-bottom: 12px;
            }

            .enterprise-detail-nameBox {
                // height: 24px;
                font-size: 14px;
                // color: var(--text-60);
                line-height: 24px;
                margin-bottom: 12px;
                align-items: center;

                .detail-content {
                    color: var(--text-100);
                    // flex: 1;
                    width: 336px;
                    height: 32px;
                    line-height: 32px;

                    :deep(.ant-input) {
                        padding: 4px 11px;
                        font-size: 14px;
                        height: 32px;
                        line-height: 32px;

                        &:hover {
                            border-color: var(--themeColor);
                        }

                        &:focus {
                            border-color: var(--themeColor);
                            box-shadow: none;
                        }
                    }
                }

                .nameBox {
                    color: var(--text-60);
                    // margin-right: 32px;
                    width: 128px;
                }
            }

            .enterprise-admin {
                margin-bottom: 0;
            }

            .illustrate {
                margin-bottom: 0;
                padding-left: 123px;
                color: var(--text-40);
            }
        }

        // .enterprise-detail-r {
        //     width: 20%;
        //     text-align: right;

        //     .m-l {
        //         margin-right: 10px;
        //     }
        // }

        .enterprise-default {
            margin-top: 49px;

            .default-title {
                height: 22px;
                font-size: 14px;
                font-family: AlibabaPuHuiTi_2_55_Regular;
                color: var(--text-40);
                line-height: 22px;
            }

            .relative-box {
                display: flex;
                align-items: center;

                .absolute-box {
                    top: 34px;
                    left: 0px;
                    z-index: 10;
                }
            }

            .inline-box {
                margin-left: 12px;
                background-color: #fff;

                span {
                    font-size: 14px;
                    font-family: AlibabaPuHuiTi_2_55_Regular;
                    color: var(--text-60);
                    line-height: 32px;
                    margin-right: 4px;
                }

                :deep(.ant-input) {
                    width: 160px;
                    padding: 4px 11px;
                    height: 32px;
                    font-size: 14px;

                    &:hover {
                        border-color: var(--themeColor);
                    }

                    &:focus {
                        border-color: var(--themeColor);
                        box-shadow: none;
                    }
                }
            }

            .picker-bt {
                padding: 16px;
                text-align: right;
                box-shadow: 0 0 10px #00000026;
                background-color: #fff;
            }

            .item-box {
                margin-top: 16px;

                .title {
                    font-size: 14px;
                    font-family: AlibabaPuHuiTi_2_55_Regular;
                    color: var(--text-60);
                    line-height: 22px;
                    width: 128px;
                }

                .logo-box {
                    width: 160px;
                    height: 92px;
                    background: #ffffff;
                    border-radius: 4px;

                    img {
                        max-height: 90px;
                    }
                }

                :deep(.orgLogo) {
                    .ant-upload {
                        margin: 0;
                        width: 160px;
                        height: 90px;
                        padding: 0px;
                        border-style: solid;
                        border-color: #d9d9d9;

                        img {
                            max-height: 90px;
                        }
                    }
                }

                :deep(.loginBaner) {
                    .ant-upload {
                        margin: 0;
                        width: 384px;
                        height: 174px;
                        padding: 0px;
                        border-style: solid;
                        border-color: #d9d9d9;

                        img {
                            max-height: 176px;
                        }
                    }
                }

                .bg-box {
                    width: 384px;
                    height: 176px;
                    border-radius: 2px;
                    border: 1px solid #d9d9d9;

                    img {
                        max-height: 176px;
                    }
                }

                .title-name-box {
                    // height: 22px;
                    font-size: 14px;
                    font-family: AlibabaPuHuiTi_2_55_Regular;
                    color: var(--text-100);
                    line-height: 32px;
                    height: 32px;

                    :deep(.ant-input) {
                        padding: 4px 11px;
                        font-size: 14px;
                        height: 32px;

                        &:hover {
                            border-color: var(--themeColor);
                        }

                        &:focus {
                            border-color: var(--themeColor);
                            box-shadow: none;
                        }
                    }
                }

                .icon-box {
                    width: 100px;
                    height: 100px;
                    border-radius: 2px;
                    border: 1px solid #d9d9d9;

                    img {
                        max-height: 100px;
                    }
                }

                :deep(.icons-up) {
                    .ant-upload {
                        margin: 0;
                        width: 100px;
                        height: 98px;
                        padding: 0px;
                        border-style: solid;
                        border-color: #d9d9d9;

                        img {
                            max-height: 98px;
                        }
                    }
                }

                .url-box {
                    font-size: 14px;
                    font-family: AlibabaPuHuiTi_2_55_Regular;
                    color: var(--text-100);
                    line-height: 32px;

                    .input-box {
                        :deep(.ant-input) {
                            width: 70%;
                            padding: 4px 11px;
                            font-size: 14px;
                            height: 32px;
                            line-height: 32px;

                            &:hover {
                                border-color: var(--themeColor);
                            }

                            &:focus {
                                border-color: var(--themeColor);
                                box-shadow: none;
                            }
                        }
                    }

                    .go-title {
                        color: rgba(149, 158, 195, 1);
                        cursor: pointer;
                        text-decoration: underline;
                    }
                }
            }

            .p-title {
                padding-left: 115px;
                height: 22px;
                font-size: 12px;
                font-family: AlibabaPuHuiTi_2_55_Regular;
                color: var(--text-40);
                line-height: 22px;
                margin-top: 4px;
            }
        }
    }

    .bt {
        padding: 4px 15px;
        font-size: 14px;
        font-family: AlibabaPuHuiTi_2_55_Regular;
        height: 32px;
    }

    .bt-regular {
        width: 120px;
        height: 32px;
        box-sizing: border-box;

        span {
            opacity: 0;
        }
    }

    :deep(.ant-select-focused) {
        .ant-select-selector {
            border-color: var(--themeColor) !important;
            box-shadow: none !important;
        }
    }

    .w40 {
        font-size: 14px;
        width: 100%;

        :deep(.ant-select) {
            font-size: 14px;
        }

        :deep(.ant-select-selector) {
            height: 32px;
            padding: 0 11px;

            .ant-select-selection-search-input {
                height: 30px;
            }

            .ant-select-selection-item {
                line-height: 30px;
            }
        }

        :deep(.ant-select-arrow) {
            right: 11px;
            width: 12px;
            height: 12px;
            margin-top: -6px;
            font-size: 12px;
        }

        :hover {
            border-color: var(--themeColor);
        }
    }

    .illustrates {
        font-size: 12px;
        font-family: AlibabaPuHuiTi_2_55_Regular;
        color: var(--text-40);
    }

    .box-1,
    .box-3 {
        width: 107px;
        display: flex;
        align-items: baseline;
        margin-left: 16px;
        flex-direction: column;
        justify-content: center;
    }

    .box-2 {
        width: 135px;
        display: flex;
        margin-left: 16px;
        align-items: baseline;
        flex-direction: column;
        justify-content: center;
    }

    .box-3 {
    }
}

:deep(.ant-switch-checked) {
    background-color: var(--themeColor);
}
.dark {
    .enterprise-detail-top .enterprise-detail .enterprise-default .inline-box {
        background-color: transparent;
        span {
        }
    }
}
</style>

<style lang="less">
.drawer-width {
    .ant-drawer-content-wrapper {
        width: 488px !important;
    }
}
</style>
