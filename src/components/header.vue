<template>
    <div :style="{ backgroundColor: $route.meta.headerBg }">
        <a-layout-header
            :style="{ backgroundColor: $route.meta.headerBg || '#f5f5f5' }"
            v-if="visible"
        >
            <div class="bg-ff dark:bg-ff-dark">
                <div
                    class="header bg-ff dark:bg-ff-dark w-content-area mx-auto my-0 xxl:m-auto"
                >
                    <!-- 左侧logo -->
                    <div class="relative leading-6 flex login-box">
                        <div class="logo cursor h-full float-right">
                            <img
                                class="h-full flex"
                                :src="loginImg?.orgLogo"
                                alt=""
                                @click="goHome"
                            />
                            <!-- <iconSvg name="furuishi" :className="'h-full-svg'"/> -->
                        </div>
                        <!-- <div
                            class="flex items-center gap-x-2 ml-4 rounded-full px-5 toggle-btn btn-hover"
                            v-if="showToggleBtn"
                            @click="toggleSystem"
                        >
                            <div>
                                {{
                                    activeSystem == 'car'
                                        ? '动力板块'
                                        : '储能板块'
                                }}
                            </div>
                            <div
                                class="w-4 h-4 flex items-center justify-center icon-box"
                            >
                                <iconSvg name="toggle" class="icon-default" />
                            </div>
                        </div> -->
                    </div>
                    <div
                        class="relative xxl:flex-1 flex justify-end image-boxs"
                    >
                        <div class="account">
                            <span
                                class="user-info"
                                v-if="!$store.getters['user/getUserInfoData']"
                                >未登录</span
                            >
                            <div class="flex items-center">
                                <a-dropdown
                                    overlayClassName="header-account-dropdown"
                                    placement="bottomRight"
                                >
                                    <div
                                        class="welcome cursor flex items-center"
                                    >
                                        <div
                                            v-if="
                                                userInfo?.realName ||
                                                userInfo?.phone
                                            "
                                        >
                                            <div class="flex items-center">
                                                <div>
                                                    <div
                                                        class="text-title dark:text-title-dark flex items-center justify-end"
                                                    >
                                                        <span
                                                            class="align-middle user-info overflow"
                                                        >
                                                            hi，{{
                                                                userInfo?.realName
                                                            }}</span
                                                        >
                                                        <img
                                                            src="@/assets/down.png"
                                                            class="w-4 h-4 ml-2"
                                                        />
                                                    </div>
                                                    <div
                                                        class="overflow commonpy text-secondar-text dark:text-60-dark"
                                                    >
                                                        {{ userInfo?.orgName }}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <template #overlay>
                                        <a-menu>
                                            <a-menu-item @click="goUser">
                                                <span class="login-out-box">
                                                    <iconSvg
                                                        name="user"
                                                        :className="'login-out '"
                                                    />
                                                    我的账户
                                                </span>
                                            </a-menu-item>
                                            <a-menu-item
                                                v-if="
                                                    isDemoUser &&
                                                    activeSystem == 'device'
                                                "
                                                @click="siteVisible = true"
                                            >
                                                <span class="login-out-box">
                                                    <iconSvg
                                                        name="addSn"
                                                        :className="'login-out'"
                                                    />
                                                    添加站点
                                                </span>
                                            </a-menu-item>
                                            <a-menu-item
                                                v-if="
                                                    isDemoUser &&
                                                    activeSystem == 'car'
                                                "
                                                @click="carSiteVisible = true"
                                            >
                                                <span class="login-out-box">
                                                    <iconSvg
                                                        name="addSn"
                                                        :className="'login-out'"
                                                    />
                                                    添加车站
                                                </span>
                                            </a-menu-item>
                                            <a-menu-item
                                                v-if="
                                                    isOperator &&
                                                    activeSystem == 'device'
                                                "
                                                @click="goOperation"
                                            >
                                                <span class="login-out-box">
                                                    <iconSvg
                                                        name="operation"
                                                        :className="'login-out'"
                                                    />
                                                    运维管理
                                                </span>
                                            </a-menu-item>
                                            <a-menu-item
                                                @click="logout"
                                                v-if="!isInDingtalk"
                                            >
                                                <span class="login-out-box">
                                                    <iconSvg
                                                        name="exit"
                                                        :className="'login-out'"
                                                    />
                                                    安全退出
                                                </span>
                                            </a-menu-item>
                                        </a-menu>
                                    </template>
                                </a-dropdown>
                            </div>
                        </div>
                        <div class="notice-box">
                            <a-dropdown
                                overlayClassName="header-account-dropdown notice-box-dropdown"
                                placement="bottomRight"
                                v-model:visible="dropdownVisible"
                            >
                                <iconSvg
                                    name="tongzhi"
                                    class="tz"
                                    :className="'set-up'"
                                    :style="{
                                        color:
                                            staffNotificationList.length > 0
                                                ? 'red'
                                                : null,
                                    }"
                                />
                                <template #overlay>
                                    <a-menu>
                                        <div class="menu-box">
                                            <div class="title">告警通知</div>
                                            <div class="risk-content">
                                                <div
                                                    class="item-risk"
                                                    v-for="item in staffNotificationList"
                                                    :key="item.id"
                                                    @click="lookRisk(item)"
                                                >
                                                    <div class="item-risk-t">
                                                        <div class="item-title">
                                                            {{ item.title }}
                                                        </div>
                                                        <div
                                                            class="item-arrow flex"
                                                        >
                                                            <iconSvg
                                                                name="jian"
                                                                :className="'margin0'"
                                                            />
                                                        </div>
                                                    </div>
                                                    <div class="item-risk-b">
                                                        <div
                                                            class="item-title-b"
                                                        >
                                                            {{ item.content }}
                                                        </div>
                                                        <div class="item-time">
                                                            {{
                                                                item.createTime
                                                            }}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div
                                                class="risk-empty"
                                                v-if="
                                                    staffNotificationList.length ==
                                                    0
                                                "
                                            >
                                                <a-empty>
                                                    <template #description>
                                                        <span
                                                            class="risk-empty-title"
                                                            >暂无异常</span
                                                        >
                                                    </template>
                                                </a-empty>
                                            </div>
                                        </div>
                                    </a-menu>
                                </template>
                            </a-dropdown>
                        </div>
                        <div class="notice-box" @click="openHelp">
                            <iconSvg
                                name="help"
                                class="help"
                                :className="'set-up'"
                            />
                        </div>
                        <div class="notice-box" @click="goRole">
                            <iconSvg
                                name="shezhi"
                                class="set"
                                :className="'set-up'"
                            />
                        </div>
                    </div>
                </div>
            </div>
        </a-layout-header>

        <alarmOverviewDetailDrawer
            v-model:visible="noticeVisible"
            :alarmId="alarmId"
            @success="refresh"
        >
        </alarmOverviewDetailDrawer>
        <edit-info
            v-model:visible="siteVisible"
            @onClose="siteVisible = false"
            :isAddNew="true"
        />
        <edit-car-info
            v-model:visible="carSiteVisible"
            @onClose="carSiteVisible = false"
            :isAddNew="true"
        />

        <el-drawer
            v-model="helpVisible"
            :size="486"
            :lockScroll="true"
            :show-close="false"
        >
            <template #header>
                <div
                    class="drawer-header flex items-center justify-between leading-5.5"
                >
                    <div class="drawer-header">
                        <span>使用指南</span>
                    </div>
                    <div>
                        <el-button plain round @click="closeDrawer">{{
                            $t('common_guanbi')
                        }}</el-button>
                    </div>
                </div>
            </template>
            <div>
                <div class="rounded-lg bg-00A0B8 mb-5">
                    <div class="px-9 py-6 leading-6 help-text">
                        <div class="text-base leading-6">
                            上善能及能源云平台使用手册
                        </div>
                        <div class="flex items-center text-sm leading-6 mb-3">
                            新手快速入门｜管理操作教程｜常见问题解答
                        </div>
                        <div>
                            <div @click="goHelp" class="look-btn">立即查看</div>
                        </div>
                    </div>
                </div>
                <div class="text-primary-text dark:text-80-dark">
                    <div class="mb-3">更新说明</div>
                    <div
                        v-for="item in updateList"
                        :key="item.date"
                        style="background-color: rgba(34, 34, 34, 0.04)"
                        class="px-4 py-3 rounded-lg mb-3 text-sm leading-6"
                    >
                        <div class="text-center mb-1">{{ item.date }}</div>
                        <div v-if="item.newVersion.length">
                            <div class="text-base">🌟 全新功能</div>
                            <div
                                v-for="ite in item.newVersion"
                                :key="ite"
                                class="pl-5 flex items-start space-x-1"
                            >
                                <div>·</div>
                                <div>{{ ite }}</div>
                            </div>
                        </div>
                        <div v-if="item.optimize.length" class="mt-3">
                            <div class="text-base mb-1">💎 体验优化</div>
                            <div
                                v-for="ite in item.optimize"
                                :key="ite"
                                class="pl-5 flex items-start space-x-1"
                            >
                                <div>·</div>
                                <div>{{ ite }}</div>
                            </div>
                        </div>
                        <div v-if="item.fixVersion.length" class="mt-3">
                            <div class="text-base mb-1">🔧 问题修复</div>
                            <div
                                v-for="ite in item.fixVersion"
                                :key="ite"
                                class="pl-5 flex items-start space-x-1"
                            >
                                <div>·</div>
                                <div>{{ ite }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </el-drawer>
    </div>
</template>

<script>
import {
    computed,
    onMounted,
    onBeforeMount,
    reactive,
    toRefs,
    ref,
    watch,
} from 'vue'
import { useStore } from 'vuex'
import { useRouter, useRoute } from 'vue-router'
import { enterpriseServiceDomain } from '@/config/env'
import { env } from 'dingtalk-jsapi'
// import CompanyLogo from "@/components/companyLogo.vue";
// import CustomerLogo from "@/components/customer-logo.vue";
import deviceService from '@/apiService/device'
import editInfo from '@/views/device/home/<USER>'
import EditCarInfo from '@/views/device/car/components/editCarInfo.vue'
import alarmOverviewDetailDrawer from '@/views/device/alarmOverviewDetailDrawer.vue'

export default {
    components: {
        editInfo,
        EditCarInfo,
        alarmOverviewDetailDrawer,
    },
    props: {},
    setup(props) {
        const isInDingtalk = computed(() => {
            return env.platform != 'notInDingTalk'
        })
        const store = useStore()
        const route = useRoute(),
            router = useRouter(),
            selectedKeys = ref(['/'])
        const userInfo = computed(() => {
            // return store.state.user.userInfo || {};
            return store.state.user.userInfoData || void 0
        })
        const activeSystem = ref(localStorage.getItem('activeSystem'))

        watch(
            () => localStorage.getItem('activeSystem'),
            (newVal, oldVal) => {
                activeSystem.value = localStorage.getItem('activeSystem')
            },
            { immediate: true, deep: true }
        )
        const gotopc = async (id) => {
            window.open(enterpriseServiceDomain, '_blank')
            // router.push("/userInfo");
        }
        const gotoUserInfo = () => {
            router.push('/userinfo')
        }

        const loginImg = computed(() => {
            return store.getters['user/getConfigData']
        })

        const state = reactive({
            visible: true,

            noticeVisible: false,
            buyTitle: '请选择购买正式版本的服务吧',
            path: '',
            bindInfo: undefined,
            alarmId: undefined,
            count: 0,
        })
        const logout = async () => {
            const {
                data: { code },
            } = await deviceService.loginOut()
            if (code === 0) {
                store.commit('device/setSelectSupplierInfo', undefined)
                store.commit('car/setSelectSupplierInfoCar', undefined)
                store.commit('user/loginOut')
                router.push({ path: '/login' })
                window.location.reload()
            }
            //跳转到官网登录页
            // window.location.href = enterpriseServiceDomain + "/#/login";
        }
        const switchEdition = async (id) => {
            await store.dispatch('user/changeTenant', id)
        }

        const helpVisible = ref(false)
        const openHelp = () => {
            helpVisible.value = true
        }
        const goHelp = () => {
            window.open(
                'https://alidocs.dingtalk.com/i/p/Pl2AmoV5q68Xdb9o5pXBW9aPOQB9om7Z',
                '_blank'
            )
        }
        const goto = ({ item, key, keyPath }) => {
            router.push(`/${key}`)
        }
        watch(
            () => route.path,
            (val) => {
                activeSystem.value = localStorage.getItem('activeSystem')
                selectedKeys.value = [val.replace('/', '')]
                state.path =
                    selectedKeys.value[0].indexOf('/') > 0
                        ? selectedKeys.value[0].slice(
                              0,
                              selectedKeys.value[0].indexOf('/')
                          )
                        : selectedKeys.value[0]
            },
            { immediate: true }
        )

        const dropdownVisible = ref(false)
        const siteVisible = ref(false)
        const carSiteVisible = ref(false)
        const siteRef = ref(null)
        const addLoading = ref(false)

        const staffNotificationList = ref([])
        const getStaffNotificationPage = async () => {
            const {
                data: {
                    code,
                    data: { records },
                },
            } = await deviceService.getStaffNotificationPage()
            if (code === 0) {
                staffNotificationList.value = records || []
                // staffNotificationList.value = [
                //     {
                //         title: '复兴能源站点 有一条新的紧急异常',
                //         content: '单体电压过压 1 级报警',
                //         createTime: '2024-01-11',
                //         id: '45',
                //     }
                // ]
            }
        }

        // getStaffNotificationPage()

        const readNotification = async (id) => {
            await deviceService.readNotification({ id })
        }

        const lookRisk = async (row) => {
            // router.push({
            //     name: 'alarmOverview',
            //     params: {
            //         id: row.bizId,
            //     },
            // })
            state.alarmId = row.bizId
            state.noticeVisible = true
            dropdownVisible.value = false
            await readNotification(row.id)
            getStaffNotificationPage()
        }

        const goRole = () => {
            // router.push({ path: '/rolePage' })
            const origin = window.location.origin
            window.open(`${origin}/#/rolePage`)
        }
        const goUser = () => {
            const origin = window.location.origin
            window.open(`${origin}/#/user`)
        }
        onBeforeMount(async () => {})
        onMounted(() => {
            getStaffNotificationPage()
            // setInterval(() => {

            // }, 1000 * 60)
        })

        const refresh = () => {
            getStaffNotificationPage()
        }

        const isDemoUser = computed(() => {
            return (
                store.state.user.userInfoData &&
                store.state.user.userInfoData?.userId != '1101'
            )
        })
        const closeDrawer = () => {
            helpVisible.value = false
        }
        const updateList = ref([
            {
                date: '2024-09',
                newVersion: [
                    '站点基础信息中，丰富了场站相关的基础资源信息如场站负载、变压器容量',
                ],
                optimize: [
                    '异常板块全面更新，所有上报异常会有专人跟进处理，进度透明，结果清晰，保障设备安全稳定运行',
                ],
                fixVersion: [],
            },
            {
                date: '2024-08',
                newVersion: [
                    '首页站点列表新增树状视图类型，通过层级关系清晰地展示各储能站点的归属信息',
                    '新增数据导出功能，支持用户便捷查看&导出站点的核心历史数据，以便于进行进一步的分析、存档或报告生成',
                    '新增操作日志查询功能，用于记录和跟踪系统中的所有关键操作，为系统管理员提供了基础的安全审计能力',
                    '增加设备故障复位功能，快捷重置特定设备状态',
                    // '站点基础信息中，丰富了场站相关的基础资源信息如场站负载、变压器容量',
                ],
                optimize: [
                    // '异常板块全面更新，所有上报异常会有专人跟进处理，进度透明，结果清晰，保障设备安全稳定运行',
                    '图表日期选择模块优化，结合设备投运日期限定可选时段',
                    '异常页摘要图表跟随列表页动态更新',
                    '异常模块相关细节逻辑优化',
                ],
                fixVersion: ['修复了个别站点充放电状态展示异常问题'],
            },
            {
                date: '2024-07',
                newVersion: [
                    '增加虚拟电厂相关场景功能，支持设备接入并进行需求响应以提升整体效益',
                    '增加AI策略优化功能，通过AI算法可有效优化设备整体运行策略以提升电芯寿命',
                    '新增操作日志查询功能，用于记录和跟踪系统中的所有关键操作，为系统管理员提供了基础的安全审计能力',
                    '增加设备故障复位功能，快捷重置特定设备状态',
                ],
                optimize: [
                    '优化了当前执行策略展示内容的结构',
                    '优化了电价模版配置阶段的校验规则及提示文案',
                    '异常模块相关细节逻辑优化',
                ],
                fixVersion: [
                    '修复了部分图表在部分浏览器显示异常的问题',
                    '修复了下发给子站点的模版，有其他站点在使用时，无法正常删除的问题',
                ],
            },
            {
                date: '2024-06',
                newVersion: [
                    '充放电策略配置模块（仅适用于如下产品型号设备：SE215-1.3（型号待定））',
                    '电价管理策略与模版配置（仅适用于如下产品型号设备：SE215-1.3（型号待定））',
                    '动环管理相关硬件控制&参数配置（仅适用于如下产品型号设备：SE215-1.3（型号待定））',
                    '增加设备故障复位功能，快捷重置特定设备状态',
                ],
                optimize: [
                    '优化了当前执行策略展示内容的结构',
                    '优化了电价模版配置阶段的校验规则及提示文案',
                    '异常模块相关细节逻辑优化',
                ],
                fixVersion: [
                    '修复了部分离线站点设备状态显示异常的问题',
                    '修复了部分图表时间选择器选择时间后无法取消的问题',
                ],
            },
            {
                date: '2024-05',
                newVersion: [
                    '新增整体运行状态示意图，直观展示电网、负载、储能等侧的用电流向及详细功率数据',
                    '支持用户将系统账号与钉钉账号进行绑定，绑定后可通过钉钉小程序进行访问',
                    '站点基础信息支持自定义更新',
                    '增加设备故障复位功能，快捷重置特定设备状态',
                ],
                optimize: [
                    '整体布局和交互进行大幅优化',
                    '添加站点逻辑优化',
                    '提升站点初始化部署体验',
                ],
                fixVersion: [
                    '修复了部分浏览器下收益排行图表比例条显示异常的问题',
                ],
            },
        ])
        const cancel = () => {}
        const toggleSystem = () => {
            if (activeSystem.value == 'car') {
                localStorage.setItem('activeSystem', 'device')
                activeSystem.value = 'device'
                router.push({
                    path: '/device',
                })
            } else {
                localStorage.setItem('activeSystem', 'car')
                activeSystem.value = 'car'
                router.push({
                    path: '/car',
                })
            }
        }
        const goHome = () => {
            if (activeSystem.value == 'car') {
                router.push({
                    path: '/car',
                })
            } else {
                router.push({
                    path: '/device',
                })
            }
        }
        const businessType = computed(() => {
            return localStorage.getItem('businessType')
        })
        const showToggleBtn = computed(() => {
            return businessType.value == 'all'
        })
        const isOperator = computed(() => {
            return store.state.user.userInfoData.roles.includes(
                'operation_staff'
            )
            // return false
        })
        const goOperation = () => {
            const origin = window.location.origin
            window.open(`${origin}/#/operation`)
        }
        return {
            ...toRefs(state),
            gotopc,
            logout,
            switchEdition,
            userInfo,
            gotoUserInfo,
            isInDingtalk,
            openHelp,
            goto,
            loginImg,
            siteVisible,
            carSiteVisible,
            siteRef,
            addLoading,
            staffNotificationList,
            lookRisk,
            goRole,
            goUser,
            dropdownVisible,
            refresh,
            isDemoUser,
            helpVisible,
            goHelp,
            closeDrawer,
            updateList,
            cancel,
            toggleSystem,
            showToggleBtn,
            goHome,
            activeSystem,
            isOperator,
            goOperation,
        }
    },
}
</script>

<style lang="less" scoped>
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 88px;
    line-height: 88px;
    width: 1440px;
    padding: 0 20px;

    // width: 100%;
    .menu {
        // width: 1200px;
        position: relative;
        height: 100%;
    }

    .login-box {
        height: 32px;

        :deep(.h-full-svg) {
            height: 32px;
            width: 156px;
        }
    }
}

@media screen and (min-width: 1921px) {
    .header {
        width: 1600px;
    }
}

.ant-layout-header {
    height: auto;
    padding: 0;
    // height: 60px;
    // line-height: 60px;
    // background-color: @body-background;
}

.logo {
    vertical-align: middle;
    flex: 1;
    display: flex;
    align-items: center;
}

.welcome,
.phone {
    line-height: 22px;
}

.setting {
    .iconfont {
        color: theme('colors.primary-text');

        &.active {
            // color: @primary-color;
        }
    }
}

.account {
    display: flex;
    align-items: center;

    .login-name {
        width: 32px;
        height: 32px;
        background: #ea0c28;
        border-radius: 50%;
        line-height: 32px;
        text-align: center;
        font-size: 12px;
        color: #fff;
        margin-right: 8px;
    }

    .user-info {
        height: 22px;
        font-size: 14px;
        font-family: AlibabaPuHuiTi_2_65_Medium;
        color: #222222;
        line-height: 22px;
    }

    .commonpy {
        height: 20px;
        font-size: 12px;
        font-family: AlibabaPuHuiTi_2_55_Regular;
        line-height: 20px;
        max-width: 160px;
        color: rgba(34, 34, 34, 0.6);
        text-align: right;
    }

    .text-sm-s {
        font-size: 14px;
        line-height: 20px;
    }
}

.bubble:after {
    content: '';
    position: absolute;
    top: 8px;
    width: 0;
    height: 0;
    border-width: 8px;
    border-style: solid;
    border-color: transparent;
    border-right-width: 8px;
    border-right-color: currentColor;
    color: #000;
    left: -13px;
}

.bubble {
    line-height: 32px;
    border-radius: 32px;
    padding: 0 20px;
    margin-left: 10px;
    position: relative;
    display: inline-block;
    // background: @primary-color;
    color: #fff;
}

.menu {
    &-item {
        display: block;
        height: 88px;
        line-height: 88px;

        &::after {
            display: block;
            content: '';
            width: 48px;
            height: 6px;
            background: transparent;
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            transition: width 0.5s ease;
        }

        .menu-item-icon,
        .menu-item-text {
            color: theme('colors.secondar-text');
        }

        &.active {
            &::after {
                width: 32px;
                // background: @primary-color;
            }

            .menu-item-icon,
            .menu-item-text {
                // color: @primary-color;
            }
        }
    }
}

.w-content-area {
    width: 1440px;
    padding: 0;
}

@media screen and (min-width: 1921px) {
    .w-content-area {
        width: 1600px;
    }
}

@media screen and (max-width: 1480px) {
    .w-content-area {
        padding: 0 20px;
    }
}

.image-boxs {
    .notice-box {
        height: 32px;
        width: 32px;
        border-radius: 50%;
        background-color: #f5f7f7;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-left: 16px;
        cursor: pointer;

        &:hover {
            :deep(.tz) {
                color: red;
            }
        }

        .help {
            color: #808080;
        }

        &:hover {
            :deep(.help) {
                color: var(--themeColor);
                opacity: 1;
            }
        }

        .set {
            color: #808080;
        }

        &:hover {
            :deep(.set) {
                color: var(--themeColor);
            }
        }
    }

    :deep(.set-up) {
        width: 20px;
        height: 20px;
        margin-right: 0;
    }
}

.toggle-btn {
    background: #f5f7f7;
    color: rgba(34, 34, 34, 0.8);
    user-select: none;
    cursor: pointer;
    transition: all 0.2s;

    &:hover {
        background: var(--themeColor);
        color: #fff;

        * {
            color: #fff;
            fill: #fff;
        }
    }
}
</style>
<style lang="less" scoped>
.header-account-dropdown {
    .ant-dropdown-menu {
        margin-top: 14px;
        width: 130px !important;
    }

    .ant-dropdown-menu-item,
    .ant-dropdown-menu-submenu-title {
        padding: 12px;
        line-height: 22px;
    }

    .ant-dropdown-menu-submenu-title > span > .anticon:first-child {
        font-size: 14px;
    }

    .ant-dropdown-menu-title-content {
        font-size: 14px;
    }

    .login-out {
        width: 16px;
        height: 16px;
        margin-right: 6px;
    }

    .login-out-box {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .margin0 {
        margin-right: 0;
        width: 18px;
        height: 18px;
    }
}

.notice-box-dropdown {
    .ant-dropdown-menu {
        width: 324px !important;
        padding-bottom: 0;

        .menu-box {
            .title {
                line-height: 52px;
                padding: 0 16px;
                border-bottom: 1px solid #d9d9d9;
                font-size: 14px;
                font-family: AlibabaPuHuiTi_2_55_Regular;
                color: #000000;
            }

            .risk-content {
                max-height: 500px;
                overflow-y: auto;

                &::-webkit-scrollbar {
                    /* Chrome Safari */
                    width: 10px;
                    /* 横向滚动条宽度为0 */
                    height: 15px;
                    /* 纵向滚动条高度为0 */
                }

                &::-webkit-scrollbar-thumb {
                    background-color: #d9d9d9;
                    border-radius: 5px;
                }

                &::-webkit-scrollbar-track {
                    background-color: #ccc;
                    border: 1px solid #ccc;
                    border-radius: 5px;
                }

                &::-webkit-scrollbar-button {
                    // background-color: #d9d9d9;
                    border-radius: 5px;
                }

                .item-risk {
                    padding: 16px;
                    border-bottom: 1px solid #d9d9d9;
                    display: flex;
                    flex-direction: column;
                    font-family: AlibabaPuHuiTi_2_55_Regular;
                    cursor: pointer;

                    .item-risk-t,
                    .item-risk-b {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;

                        .item-title {
                            font-size: 14px;

                            color: rgba(0, 0, 0, 0.4);
                        }

                        .item-title-b {
                            font-size: 14px;
                            color: #000000;
                        }

                        .item-time {
                            font-size: 14px;
                            color: rgba(0, 0, 0, 0.4);
                        }

                        .item-arrow {
                            font-size: 18px;
                        }
                    }

                    .item-risk-b {
                        margin-top: 8px;
                    }
                }
            }

            .risk-empty {
                padding: 16px 0;

                .risk-empty-title {
                    font-size: 14px;
                    font-family: AlibabaPuHuiTi_2_55_Regular;
                    color: var(--text-100);
                }
            }
        }
    }
}

.help-text {
    color: rgba(255, 255, 255, 0.85);
}

.look-btn {
    color: var(--themeColor);
    width: 88px;
    line-height: 32px;
    background: #fff;
    border-radius: 4px;
    text-align: center;
    cursor: pointer;
    user-select: none;
    transition: all 0.2s;

    &:hover {
        opacity: 0.95;
    }
}

.dropdown-menu {
    width: 108px;
    text-align: center;
}
</style>
