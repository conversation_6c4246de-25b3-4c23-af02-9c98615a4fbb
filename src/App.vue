<template>
    <a-config-provider
        :locale="configLang"
        :transformCellText="({ text, column, record, index }) => text || '-'"
        autoInsertSpaceInButton
    >
        <!-- calc(100vh - 60px) -->
        <!--  style="min-height: 100vh; min-width: 100vh" -->
        <a-layout>
            <router-view />
            <!-- <div
                class="fixed right-0 bottom-0 z-1000 text-60 opacity-10"
                v-if="locale == 'en'"
            >
                e
            </div>
            <div
                class="fixed right-0 bottom-0 z-1000 text-60 opacity-10"
                v-if="locale == 'zh'"
            >
                z
            </div> -->
        </a-layout>
    </a-config-provider>
</template>

<script>
import {
    defineComponent,
    provide,
    onBeforeMount,
    onMounted,
    ref,
    computed,
    onBeforeUnmount,
    onUnmounted,
} from 'vue'
import zhCN from 'ant-design-vue/es/locale/zh_CN'
import enUS from 'ant-design-vue/es/locale/en_US'
import deviceService from '@/apiService/device'
import { useStore } from 'vuex'
import hooks from './views/role/components/hooks'
import {
    setThemeColor,
    setBusinessType,
    getBrowserLanguage,
    getPublicIP,
    getCountry,
} from '@/common/util.js'
import Cookies from 'js-cookie'
import { useI18n } from 'vue-i18n'
import { useRoute, useRouter } from 'vue-router'
import { whiteRouteList } from '@/utils/whiteRouteList.js'

// import { fetchMessages } from '@/config/lang/i18nService.js'
export default defineComponent({
    name: 'app',
    setup() {
        const store = useStore()
        const { locale, setLocaleMessage } = useI18n()
        const router = useRouter()
        //获取用户信息
        store.commit('user/checkLogin')
        if (store.getters['user/isLogin']) {
            store.dispatch('user/getBasicInfo')
        }

        // const loadMessages = async (lang) => {
        //     // const messages = await fetchMessages(lang)
        //     // setLocaleMessage(lang, messages)
        // }
        let updateInterval
        const getConfigLang = (lang) => {
            if (lang == 'zh') {
                return zhCN
            } else if (lang == 'en') {
                return enUS
            } else {
                return zhCN
            }
        }
        const route = useRoute()
        const currentPage = computed(() => {
            return whiteRouteList.includes(route.path)
        })
        const configLang = ref(zhCN)

        onBeforeMount(async () => {
            const configData = store.state.user.configData
            if (configData && configData.themeColor) {
                document.getElementById('title').innerHTML =
                    configData.webPageTitle
                document.getElementById('icon-svg').href =
                    configData.webPageIcon
                document
                    .getElementsByTagName('body')[0]
                    .style.setProperty('--themeColor', configData.themeColor)
                store.commit('user/setConfigData', configData)
                setThemeColor(configData.themeColor)
            }
            let langVal = getBrowserLanguage()
            let oldLang = localStorage.getItem('language')
            locale.value = oldLang || langVal || 'zh'
            // locale.value = localStorage.getItem('language') || 'zh'
            await store.dispatch('lang/changeLanguage', locale.value)
            configLang.value = getConfigLang(locale.value)
            await router.isReady()
            if (
                currentPage.value &&
                route.path != '/login' &&
                route.path != '/Login'
            ) {
                //
                hooks.setDeflutColor()
                store.commit('user/setConfigData', hooks.config)
            } else {
                if (configData && configData.themeColor) {
                    //
                } else {
                    await getDetail()
                }
            }
            // store.dispatch('user/getUserColorDetail')
            updateInterval = setInterval(async () => {
                // await loadMessages(locale.value)
            }, 30 * 60 * 1000) // 30分钟
        })
        // 加载语言包
        onUnmounted(() => {
            clearInterval(updateInterval)
        })

        provide(
            'ossCommonResource',
            'https://ming-enterprise-oss.mingwork.com/'
        )

        const getApi = async () => {
            const host = window.location.host
            const token = store.getters['user/getNewToken']
            console.log(token)
            if (token) {
                return await deviceService.getCurrentOrgData()
            } else {
                console.log(2)
                return await deviceService.getOrgWebpageSettingByDomain({
                    domain: host,
                })
            }
        }

        const getDetail = async () => {
            try {
                const {
                    data: { code, data },
                } = await getApi()
                if (code === 0) {
                    if (data) {
                        setBusinessType(data.businessType)
                        const params = {
                            orgLogo: data.orgLogo,
                            loginBanner: data.loginBanner,
                            themeColor: data.themeColor,
                            webPageIcon: data.webPageIcon,
                            webPageTitle: data.webPageTitle,
                        }
                        const obj = hooks.setColor(params)
                        document.getElementById('title').innerHTML =
                            obj.webPageTitle
                        document.getElementById('icon-svg').href =
                            obj.webPageIcon
                        document
                            .getElementsByTagName('body')[0]
                            .style.setProperty('--themeColor', obj.themeColor)
                        store.commit('user/setConfigData', obj)
                        setThemeColor(obj.themeColor)
                    } else {
                        hooks.setDeflutColor()
                        store.commit('user/setConfigData', hooks.config)
                    }
                } else {
                    hooks.setDeflutColor()
                    store.commit('user/setConfigData', hooks.config)
                }
            } catch (error) {
                hooks.setDeflutColor()
                store.commit('user/setConfigData', hooks.config)
            }
        }
        const changeLang = async () => {
            if (locale.value == 'zh') {
                locale.value = 'en'
            } else {
                locale.value = 'zh'
            }
            await store.dispatch('lang/changeLanguage', locale.value)
            // window.location.reload()
        }
        // 页面销毁时
        onBeforeUnmount(() => {
            Cookies.set('hideOnce', 0, {
                expires: 7,
                path: '/',
                domain: 'ssnj.com',
            })
        })
        return {
            zhCN,
            enUS,
            configLang,
            locale,
            changeLang,
        }
    },
})
</script>
<style>
[v-cloak] {
    display: none;
}
</style>
