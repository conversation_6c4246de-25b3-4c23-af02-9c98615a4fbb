// store/lang.js
import service from '@/apiService';
// import { saveMessages, getMessage, getAllMessages } from '@/config/indexedDBService';
import { getCachedMessages, cacheMessages } from '@/config/indexedDBService';
const state = () => {
  return {
    currentLanguage: localStorage.getItem('language') || 'zh',
    messages: {},
  }
}
const mutations = {
  setLanguage(state, language) {
    state.currentLanguage = language;
  },
  setMessages(state, messages) {
    state.messages = messages;
  },
}
const actions = {
  async changeLanguage({ commit, dispatch }, language) {
    console.log('store',language);
    localStorage.setItem('language', language);
    commit('setLanguage', language);
    await dispatch('fetchMessages', language);
  },
  async fetchMessages() {
    // const language = localStorage.getItem('language')
    // try {
    //   const response = await service.getMessages(language);
    //   const messagesArray = response.data.data;
    //   const messages = messagesArray.reduce((acc, item) => {
    //     acc[item.messageCode] = item.messageText;
    //     return acc;
    //   }, {});
    //   // 缓存语言包
    //   return messages;
    // } catch (error) {
    //   console.error('Failed to fetch messages:', error);
    //   return {};
    // }
  },
}
const getters = {
  currentLanguage(state) {
    return state.currentLanguage
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions,
}
